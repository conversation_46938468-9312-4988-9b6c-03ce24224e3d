"""
Advanced Visualization Engine for AI Data Analysis Platform
Generates comprehensive plots automatically based on data characteristics

This module creates as many relevant visualizations as possible from your data:
- Automatic plot type selection based on data types
- Interactive Plotly charts for web display
- Statistical plots for deeper insights
- Business intelligence visualizations
- Time series analysis plots
- Correlation and relationship analysis
- Distribution analysis
- Trend analysis
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
import plotly.figure_factory as ff
from plotly.subplots import make_subplots
import base64
import io
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

class AdvancedVisualizationEngine:
    """
    Revolutionary visualization engine that automatically generates 
    comprehensive plots based on data characteristics
    """
    
    def __init__(self):
        """Initialize the visualization engine"""
        # Set modern styling
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
        # Plotly template for consistent styling
        self.plotly_template = "plotly_white"
        
        logger.info("🎨 Advanced Visualization Engine initialized")
    
    def _make_json_serializable(self, obj) -> Any:
        """Convert numpy/pandas data types to JSON serializable Python types"""
        if isinstance(obj, np.ndarray):
            return obj.tolist()  # Convert NumPy arrays to lists first
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, pd.Timestamp):
            return obj.isoformat()
        elif isinstance(obj, tuple):
            return list(obj)
        elif hasattr(obj, 'dtype') and hasattr(obj, 'tolist'):  # Handle pandas/numpy objects with tolist method
            try:
                return obj.tolist()
            except:
                return str(obj)
        elif hasattr(obj, 'dtype'):  # Handle other pandas/numpy dtypes
            return str(obj)
        elif isinstance(obj, type) and hasattr(obj, '__module__') and 'numpy' in str(obj.__module__):
            return str(obj)
        elif isinstance(obj, dict):
            return {key: self._make_json_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._make_json_serializable(item) for item in obj]
        else:
            # Try to serialize, fallback to string representation
            try:
                import json
                json.dumps(obj)
                return obj
            except (TypeError, ValueError):
                # Check if it's a NumPy type we missed
                if hasattr(obj, 'tolist'):
                    try:
                        return obj.tolist()
                    except:
                        pass
                return str(obj)

    def generate_comprehensive_plots(self, df: pd.DataFrame, file_metadata: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Generate comprehensive visualizations automatically
        
        Args:
            df: DataFrame to visualize
            file_metadata: Metadata about the file
            
        Returns:
            Dictionary containing all generated plots
        """
        try:
            logger.info(f"🎨 Generating comprehensive plots for data with shape {df.shape}")
            
            plots = {}
            
            # Data preprocessing
            df_clean = self._preprocess_data(df)
            
            # 1. OVERVIEW PLOTS
            try:
                plots.update(self._generate_overview_plots(df_clean))
                logger.info("✅ Overview plots generated")
            except Exception as e:
                logger.error(f"❌ Overview plots failed: {e}")
            
            # 2. NUMERIC ANALYSIS PLOTS
            numeric_cols = df_clean.select_dtypes(include=[np.number]).columns.tolist()
            if len(numeric_cols) > 0:
                try:
                    plots.update(self._generate_numeric_plots(df_clean, numeric_cols))
                    logger.info("✅ Numeric plots generated")
                except Exception as e:
                    logger.error(f"❌ Numeric plots failed: {e}")
            
            # 3. CATEGORICAL ANALYSIS PLOTS  
            categorical_cols = df_clean.select_dtypes(include=['object', 'category']).columns.tolist()
            if len(categorical_cols) > 0:
                try:
                    plots.update(self._generate_categorical_plots(df_clean, categorical_cols))
                    logger.info("✅ Categorical plots generated")
                except Exception as e:
                    logger.error(f"❌ Categorical plots failed: {e}")
            
            # 4. TIME SERIES PLOTS
            date_cols = self._detect_date_columns(df_clean)
            if len(date_cols) > 0:
                try:
                    plots.update(self._generate_time_series_plots(df_clean, date_cols, numeric_cols))
                    logger.info("✅ Time series plots generated")
                except Exception as e:
                    logger.error(f"❌ Time series plots failed: {e}")
            
            # 5. CORRELATION AND RELATIONSHIP PLOTS
            if len(numeric_cols) > 1:
                try:
                    plots.update(self._generate_correlation_plots(df_clean, numeric_cols))
                    logger.info("✅ Correlation plots generated")
                except Exception as e:
                    logger.error(f"❌ Correlation plots failed: {e}")
            
            # 6. DISTRIBUTION PLOTS
            try:
                plots.update(self._generate_distribution_plots(df_clean, numeric_cols, categorical_cols))
                logger.info("✅ Distribution plots generated")
            except Exception as e:
                logger.error(f"❌ Distribution plots failed: {e}")
            
            # 7. BUSINESS INTELLIGENCE PLOTS
            try:
                plots.update(self._generate_business_intelligence_plots(df_clean, numeric_cols, categorical_cols))
                logger.info("✅ Business Intelligence plots generated")
            except Exception as e:
                logger.error(f"❌ Business Intelligence plots failed: {e}")
            
            # 8. ADVANCED STATISTICAL PLOTS
            try:
                plots.update(self._generate_statistical_plots(df_clean, numeric_cols))
                logger.info("✅ Statistical plots generated")
            except Exception as e:
                logger.error(f"❌ Statistical plots failed: {e}")
            
            # 9. INTERACTIVE DASHBOARD PLOTS
            try:
                plots.update(self._generate_dashboard_plots(df_clean, numeric_cols, categorical_cols))
                logger.info("✅ Dashboard plots generated")
            except Exception as e:
                logger.error(f"❌ Dashboard plots failed: {e}")
            
            logger.info(f"✅ Generated {len(plots)} comprehensive plots")
            
            result = {
                "total_plots": len(plots),
                "plot_categories": self._categorize_plots(plots),
                "plots": plots,
                "data_summary": {
                    "total_rows": int(len(df_clean)),
                    "total_columns": int(len(df_clean.columns)),
                    "numeric_columns": int(len(numeric_cols)),
                    "categorical_columns": int(len(categorical_cols)),
                    "date_columns": int(len(date_cols))
                }
            }
            
            # Make the result JSON serializable
            serialized_result = self._make_json_serializable(result)
            
            # Ensure we return a dictionary
            if isinstance(serialized_result, dict):
                return serialized_result
            else:
                return result  # Fallback to original result
            
        except Exception as e:
            logger.error(f"❌ Visualization generation failed: {e}")
            return {
                "total_plots": 0,
                "error": str(e),
                "plots": {}
            }
    
    def _preprocess_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Preprocess data for better visualization"""
        df_clean = df.copy()
        
        # Convert string dates to datetime
        for col in df_clean.columns:
            if df_clean[col].dtype == 'object':
                # Try to convert to datetime
                try:
                    pd.to_datetime(df_clean[col], errors='raise')
                    df_clean[col] = pd.to_datetime(df_clean[col])
                except:
                    pass
        
        return df_clean
    
    def _detect_date_columns(self, df: pd.DataFrame) -> List[str]:
        """Detect date/datetime columns"""
        date_cols = []
        
        for col in df.columns:
            if pd.api.types.is_datetime64_any_dtype(df[col]):
                date_cols.append(col)
            elif df[col].dtype == 'object':
                # Check if it looks like dates
                sample = df[col].dropna().astype(str).iloc[:10]
                try:
                    pd.to_datetime(sample, errors='raise')
                    date_cols.append(col)
                except:
                    pass
        
        return date_cols
    
    def _generate_overview_plots(self, df: pd.DataFrame) -> Dict:
        """Generate overview plots"""
        plots = {}
        
        # 1. Data Shape Overview
        fig = go.Figure(data=[
            go.Bar(name='Dimensions', x=['Rows', 'Columns'], y=[len(df), len(df.columns)],
                   marker_color=['#FF6B6B', '#4ECDC4'])
        ])
        fig.update_layout(
            title="📊 Data Overview: Rows vs Columns",
            template=self.plotly_template,
            height=400
        )
        plots['data_overview'] = fig.to_html(include_plotlyjs=True, div_id="data_overview")
        
        # 2. Data Types Distribution
        type_counts = df.dtypes.value_counts()
        fig = px.pie(
            values=type_counts.values, 
            names=[str(x) for x in type_counts.index],  # Convert to strings
            title="🔢 Data Types Distribution"
        )
        fig.update_layout(template=self.plotly_template, height=400)
        plots['data_types'] = fig.to_html(include_plotlyjs=True, div_id="data_types")
        
        # 3. Missing Data Analysis
        missing_data = df.isnull().sum()
        missing_data = missing_data[missing_data > 0]
        
        if len(missing_data) > 0:
            fig = px.bar(
                x=missing_data.index, 
                y=missing_data.values,
                title="🚨 Missing Data Analysis",
                labels={'x': 'Columns', 'y': 'Missing Values'}
            )
            fig.update_layout(template=self.plotly_template, height=400)
            plots['missing_data'] = fig.to_html(include_plotlyjs=True, div_id="missing_data")
        
        return plots
    
    def _generate_numeric_plots(self, df: pd.DataFrame, numeric_cols: List[str]) -> Dict:
        """Generate comprehensive numeric analysis plots"""
        plots = {}
        
        # 1. Box plots for first few columns
        for i, col in enumerate(numeric_cols[:3]):
            fig = px.box(df, y=col, title=f"📊 {col}: Distribution Analysis")
            fig.update_layout(template=self.plotly_template, height=400)
            plots[f'box_{col}'] = fig.to_html(include_plotlyjs=True, div_id=f"box_{col}")
        
        # 2. Histograms  
        for i, col in enumerate(numeric_cols[:3]):
            fig = px.histogram(df, x=col, marginal="box", title=f"📊 {col}: Frequency Distribution")
            fig.update_layout(template=self.plotly_template, height=400)
            plots[f'hist_{col}'] = fig.to_html(include_plotlyjs=True, div_id=f"hist_{col}")
        
        return plots
    
    def _generate_categorical_plots(self, df: pd.DataFrame, categorical_cols: List[str]) -> Dict:
        """Generate categorical analysis plots"""
        plots = {}
        
        for i, col in enumerate(categorical_cols[:3]):
            value_counts = df[col].value_counts().head(10)
            
            # Bar chart
            fig = px.bar(
                x=value_counts.index, 
                y=value_counts.values,
                title=f"📊 {col}: Category Distribution",
                labels={'x': col, 'y': 'Count'}
            )
            fig.update_layout(template=self.plotly_template, height=400)
            plots[f'cat_bar_{col}'] = fig.to_html(include_plotlyjs=True, div_id=f"cat_bar_{col}")
        
        return plots
    
    def _generate_time_series_plots(self, df: pd.DataFrame, date_cols: List[str], numeric_cols: List[str]) -> Dict:
        """Generate time series analysis plots"""
        plots = {}
        
        for date_col in date_cols[:1]:
            df_sorted = df.sort_values(date_col)
            
            for num_col in numeric_cols[:2]:
                fig = px.line(
                    df_sorted, 
                    x=date_col, 
                    y=num_col,
                    title=f"📈 Time Series: {num_col} over {date_col}"
                )
                fig.update_layout(template=self.plotly_template, height=400)
                plots[f'timeseries_{num_col}_{date_col}'] = fig.to_html(include_plotlyjs=True, div_id=f"timeseries_{num_col}_{date_col}")
        
        return plots
    
    def _generate_correlation_plots(self, df: pd.DataFrame, numeric_cols: List[str]) -> Dict:
        """Generate correlation and relationship plots"""
        plots = {}
        
        # Correlation Matrix Heatmap
        try:
            corr_matrix = df[numeric_cols].corr()
            fig = px.imshow(
                corr_matrix,
                text_auto=True,
                aspect="auto",
                title="🔗 Correlation Matrix: Relationship Strength",
                color_continuous_scale='RdBu'
            )
            fig.update_layout(template=self.plotly_template, height=500)
            plots['correlation_matrix'] = fig.to_html(include_plotlyjs=True, div_id="correlation_matrix")
        except Exception as e:
            logger.error(f"Correlation matrix failed: {e}")
        
        return plots
    
    def _generate_distribution_plots(self, df: pd.DataFrame, numeric_cols: List[str], categorical_cols: List[str]) -> Dict:
        """Generate distribution analysis plots"""
        plots = {}
        
        # Box plots by category
        if len(categorical_cols) > 0 and len(numeric_cols) > 0:
            try:
                cat_col = categorical_cols[0]
                num_col = numeric_cols[0]
                
                fig = px.box(
                    df, 
                    x=cat_col, 
                    y=num_col,
                    title=f"📦 Distribution by Category: {num_col} by {cat_col}"
                )
                fig.update_layout(template=self.plotly_template, height=400)
                plots[f'box_by_category_{cat_col}_{num_col}'] = fig.to_html(include_plotlyjs=True, div_id=f"box_by_category_{cat_col}_{num_col}")
            except Exception as e:
                logger.error(f"Distribution plot failed: {e}")
        
        return plots
    
    def _generate_business_intelligence_plots(self, df: pd.DataFrame, numeric_cols: List[str], categorical_cols: List[str]) -> Dict:
        """Generate business intelligence specific plots"""
        plots = {}
        
        if len(numeric_cols) > 0:
            try:
                # Simple business metrics
                fig = go.Figure(data=[
                    go.Bar(name='Metrics', x=numeric_cols[:3], y=[df[col].sum() for col in numeric_cols[:3]],
                           marker_color='#FF6B6B')
                ])
                fig.update_layout(
                    title="💼 Business Metrics Dashboard",
                    template=self.plotly_template,
                    height=400
                )
                plots['business_metrics'] = fig.to_html(include_plotlyjs=True, div_id="business_metrics")
            except Exception as e:
                logger.error(f"Business Intelligence plot failed: {e}")
        
        return plots
    
    def _generate_statistical_plots(self, df: pd.DataFrame, numeric_cols: List[str]) -> Dict:
        """Generate advanced statistical plots"""
        plots = {}
        
        # Simple statistical summary
        if len(numeric_cols) > 0:
            try:
                fig = go.Figure()
                for col in numeric_cols[:2]:
                    fig.add_trace(go.Histogram(x=df[col], name=col, opacity=0.7))
                
                fig.update_layout(
                    title="📊 Statistical Distribution Comparison",
                    template=self.plotly_template,
                    height=400,
                    barmode='overlay'
                )
                plots['statistical_comparison'] = fig.to_html(include_plotlyjs=True, div_id="statistical_comparison")
            except Exception as e:
                logger.error(f"Statistical plot failed: {e}")
        
        return plots
    
    def _generate_dashboard_plots(self, df: pd.DataFrame, numeric_cols: List[str], categorical_cols: List[str]) -> Dict:
        """Generate interactive dashboard-style plots"""
        plots = {}
        
        if len(numeric_cols) > 0:
            try:
                # Executive summary dashboard
                fig = make_subplots(
                    rows=2, cols=2,
                    subplot_titles=["Key Metrics", "Trend Analysis", "Distribution", "Summary"],
                    specs=[[{"type": "bar"}, {"type": "scatter"}],
                           [{"type": "histogram"}, {"type": "indicator"}]]
                )
                
                # Add traces
                col1 = numeric_cols[0]
                fig.add_trace(
                    go.Bar(x=['Total', 'Average', 'Max'], 
                          y=[df[col1].sum(), df[col1].mean(), df[col1].max()],
                          name=col1),
                    row=1, col=1
                )
                
                if len(numeric_cols) > 1:
                    col2 = numeric_cols[1]
                    fig.add_trace(
                        go.Scatter(x=list(range(len(df))), y=df[col2], 
                                  mode='lines+markers', name=col2),
                        row=1, col=2
                    )
                
                fig.add_trace(
                    go.Histogram(x=df[col1], name='Distribution'),
                    row=2, col=1
                )
                
                avg_value = df[col1].mean()
                fig.add_trace(
                    go.Indicator(
                        mode="gauge+number",
                        value=avg_value,
                        title={'text': f"Average {col1}"},
                        gauge={'axis': {'range': [None, df[col1].max()]},
                               'bar': {'color': "#FF6B6B"}}
                    ),
                    row=2, col=2
                )
                
                fig.update_layout(
                    title="🎛️ Executive Dashboard: Complete Business Overview",
                    template=self.plotly_template,
                    height=700
                )
                plots['executive_dashboard'] = fig.to_html(include_plotlyjs=True, div_id="executive_dashboard")
                
            except Exception as e:
                logger.error(f"Dashboard plot failed: {e}")
        
        return plots
    
    def _categorize_plots(self, plots: Dict) -> Dict:
        """Categorize plots for better organization"""
        categories = {
            "overview": [],
            "numeric_analysis": [],
            "categorical_analysis": [],
            "time_series": [],
            "correlations": [],
            "distributions": [],
            "business_intelligence": [],
            "statistical_analysis": [],
            "dashboards": []
        }
        
        for plot_name in plots.keys():
            if any(x in plot_name for x in ['overview', 'data_types', 'missing']):
                categories["overview"].append(plot_name)
            elif any(x in plot_name for x in ['box_', 'hist_']):
                categories["numeric_analysis"].append(plot_name)
            elif any(x in plot_name for x in ['cat_']):
                categories["categorical_analysis"].append(plot_name)
            elif any(x in plot_name for x in ['timeseries', 'trend']):
                categories["time_series"].append(plot_name)
            elif any(x in plot_name for x in ['correlation', 'scatter']):
                categories["correlations"].append(plot_name)
            elif any(x in plot_name for x in ['distribution', 'box_by_category']):
                categories["distributions"].append(plot_name)
            elif any(x in plot_name for x in ['business', 'metrics']):
                categories["business_intelligence"].append(plot_name)
            elif any(x in plot_name for x in ['statistical']):
                categories["statistical_analysis"].append(plot_name)
            elif any(x in plot_name for x in ['dashboard', 'executive']):
                categories["dashboards"].append(plot_name)
        
        return categories
