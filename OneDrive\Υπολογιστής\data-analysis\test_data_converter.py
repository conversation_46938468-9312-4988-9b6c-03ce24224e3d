#!/usr/bin/env python3
"""
Test the DataConverter to ensure it properly standardizes file formats
"""

import pandas as pd
import io
from core.data_converter import DataConverter

def test_data_converter():
    """Test the DataConverter with sample data"""
    print("🧪 Testing DataConverter...")
    
    # Create sample CSV data
    sample_csv = """Date,Revenue,Orders,Customers,Category
2024-01-01,15000,120,450,Electronics
2024-01-02,17500,135,520,Electronics
2024-01-03,12000,95,380,Clothing
2024-01-04,18000,145,540,Electronics
2024-01-05,14500,110,420,Clothing"""
    
    # Initialize converter
    converter = DataConverter()
    
    # Convert CSV data
    csv_bytes = sample_csv.encode('utf-8')
    result = converter.convert_to_standard_format(csv_bytes, "test_data.csv")
    
    print(f"✅ Conversion successful: {result['metadata']['conversion_success']}")
    print(f"📊 Data shape: {result['metadata']['total_rows']} rows, {result['metadata']['total_columns']} columns")
    print(f"📋 Columns: {result['columns']}")
    print(f"🔍 Data types: {result['metadata']['data_types']}")
    print(f"📝 First record: {result['records'][0]}")
    
    # Verify the standardized format
    assert result['metadata']['conversion_success'] == True
    assert len(result['records']) == 5
    assert len(result['columns']) == 5
    assert 'Date' in result['columns']
    assert 'Revenue' in result['columns']
    
    print("🎉 DataConverter test passed!")
    return result

if __name__ == "__main__":
    test_data_converter()
