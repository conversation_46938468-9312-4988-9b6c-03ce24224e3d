# AgentUniverse Configuration for AgentUniverseOrchestrator
name: "AgentUniverseOrchestrator"
description: "Master controller that orchestrates the complete PEER + DOE agent swarm workflow"
agent_type: "react"

class_name: "agents.orchestrator.agent_universe_orchestrator.agent_universe_orchestrator.AgentUniverseOrchestrator"

# The orchestrator might use LLM for high-level decision making and workflow coordination
llm:
  type: "deepseek_llm"
  model: "deepseek-v3" # Most capable model for orchestration decisions
  # llm_config_name: "deepseek_orchestrator_config"

prompt:
  version: "orchestrator_workflow_v1"
  # Template for orchestration decision making
  # prompt_template: |
  #   You are the master orchestrator for an AI business intelligence agent swarm.
  #   
  #   User Query: {user_query}
  #   DeepSeek Pre-Analysis: {deepseek_preanalysis}
  #   File Data Structure: {file_data}
  #   Workflow Configuration: {workflow_config}
  #   
  #   Your task is to coordinate the optimal execution strategy for the agent swarm.
  #   
  #   Determine:
  #   1. Which agents to activate based on the analysis requirements
  #   2. Optimal execution order and dependencies
  #   3. Resource allocation and parallel processing opportunities
  #   4. Quality checkpoints and validation steps
  #   5. Error handling and fallback strategies
  #   
  #   Consider the PEER pattern (Plan/Execute/Express/Review) and DOE pattern (Data-fining/Opinion-inject/Express).
  #   
  #   Output your orchestration strategy in JSON format:
  #   {
  #     "execution_strategy": {
  #       "pattern_selection": "PEER+DOE|PEER_only|DOE_only|custom",
  #       "agent_activation_sequence": [...],
  #       "parallel_processing_groups": [...],
  #       "dependencies": {...},
  #       "estimated_execution_time": "..."
  #     },
  #     "resource_allocation": {
  #       "cpu_intensive_agents": [...],
  #       "llm_intensive_agents": [...],
  #       "memory_requirements": {...}
  #     },
  #     "quality_assurance": {
  #       "validation_checkpoints": [...],
  #       "error_handling_strategy": "...",
  #       "fallback_procedures": [...]
  #     }
  #   }

output_parser:
  class_name: "agent_universe_orchestrator.AgentUniverseOrchestratorOutputParser"

# Configuration for agent management
agent_management:
  max_parallel_agents: 5
  timeout_seconds: 300
  retry_attempts: 3
  
# Workflow patterns
patterns:
  peer:
    enabled: true
    phases: ["plan", "execute", "express", "review"]
  doe:
    enabled: true
    phases: ["data_fining", "opinion_inject", "express"]
  
# Input and output examples
# input_keys: ["user_query", "file_data", "deepseek_preanalysis", "workflow_config"]  
# output_keys: ["peer_results", "doe_results", "final_business_intelligence_report", "orchestration_summary"]
