#!/usr/bin/env python3
"""
Debug script to check what the web interface is actually returning
"""

import asyncio
import json
from main_platform import AIDataAnalysisPlatform

async def test_response_structure():
    """Test what the platform actually returns"""
    print("🔍 Testing response structure...")
    
    # Initialize platform
    platform = AIDataAnalysisPlatform()
    
    # Test with hemoglobe1.xlsx if it exists
    import os
    if os.path.exists("hemoglobe1.xlsx"):
        print("📁 Found test file: hemoglobe1.xlsx")
        
        # Read the file
        with open("hemoglobe1.xlsx", "rb") as f:
            file_content = f.read()
        
        # Analyze
        print("🚀 Starting analysis...")
        result = await platform.analyze_business_data(
            file_path="hemoglobe1.xlsx",
            file_content=file_content
        )
        
        print("\n" + "="*50)
        print("📊 RESPONSE STRUCTURE:")
        print("="*50)
        
        # Print the keys in the result
        if isinstance(result, dict):
            print("🔑 Top-level keys:")
            for key in result.keys():
                print(f"  - {key}")
                
            # Check for visualization data
            print("\n🎨 Checking for visualization data...")
            
            # Check various possible paths
            viz_paths = [
                'comprehensive_visualizations',
                'agent_orchestration.visualization_results',
                'visualization_results',
                'plots',
                'visualizations'
            ]
            
            for path in viz_paths:
                keys = path.split('.')
                current = result
                try:
                    for key in keys:
                        current = current[key]
                    print(f"  ✅ Found data at: {path}")
                    if isinstance(current, dict):
                        print(f"     Keys: {list(current.keys())}")
                        if 'total_plots' in current:
                            print(f"     Total plots: {current['total_plots']}")
                        if 'plots' in current:
                            print(f"     Plot count: {len(current['plots']) if current['plots'] else 0}")
                except (KeyError, TypeError):
                    print(f"  ❌ No data at: {path}")
            
            # Try to save the full structure to a file for inspection
            try:
                # Make it JSON serializable for inspection
                from web_interface import make_json_serializable
                serializable = make_json_serializable(result)
                
                with open("debug_response_structure.json", "w") as f:
                    json.dump(serializable, f, indent=2, ensure_ascii=False)
                print(f"\n💾 Full response saved to: debug_response_structure.json")
                
            except Exception as e:
                print(f"\n❌ Could not save response: {e}")
                
        else:
            print(f"❌ Result is not a dict, it's: {type(result)}")
            
    else:
        print("❌ Test file hemoglobe1.xlsx not found")

if __name__ == "__main__":
    asyncio.run(test_response_structure())
