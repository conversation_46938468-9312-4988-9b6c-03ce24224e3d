"""
DataFiningAgent: Refines and optimizes data for analysis as part of the DOE Data-fining phase.
"""
from agentuniverse.agent.agent import Agent
from typing import Any, Dict, List
import pandas as pd
import numpy as np
import json

class DataFiningAgent(Agent):
    """Refines raw business data into analysis-ready format with enhanced quality and structure."""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def input_keys(self) -> list[str]:
        return ["raw_dataset", "business_context", "quality_requirements", "analysis_objectives"]

    def output_keys(self) -> list[str]:
        return ["refined_dataset", "data_quality_report", "transformation_summary", "optimization_metrics"]

    def parse_input(self, input_object, agent_input):
        return {
            "raw_dataset": agent_input.get("raw_dataset"),
            "business_context": agent_input.get("business_context", {}),
            "quality_requirements": agent_input.get("quality_requirements", {}),
            "analysis_objectives": agent_input.get("analysis_objectives", [])
        }

    def parse_result(self, agent_result):
        return agent_result

    def execute(self, input_object, agent_input):
        """
        Refine raw data into high-quality, analysis-ready format.
        """
        raw_dataset = agent_input.get("raw_dataset")
        business_context = agent_input.get("business_context", {})
        quality_requirements = agent_input.get("quality_requirements", {})
        analysis_objectives = agent_input.get("analysis_objectives", [])

        if raw_dataset is None:
            return {"error": "No raw dataset provided", "success": False}

        try:
            # Convert to DataFrame if needed
            if isinstance(raw_dataset, dict):
                df = pd.DataFrame(raw_dataset)
            elif isinstance(raw_dataset, list):
                df = pd.DataFrame(raw_dataset)
            elif isinstance(raw_dataset, pd.DataFrame):
                df = raw_dataset.copy()
            else:
                return {"error": "Unsupported dataset format", "success": False}

            # Store original state for comparison
            original_shape = df.shape
            original_quality = self._assess_initial_quality(df)

            # Apply data fining transformations
            refined_df = self._apply_data_cleaning(df)
            refined_df = self._apply_data_enhancement(refined_df, business_context)
            refined_df = self._apply_optimization(refined_df, analysis_objectives)

            # Generate quality report
            quality_report = self._generate_quality_report(df, refined_df, original_quality)

            # Create transformation summary
            transformation_summary = self._create_transformation_summary(df, refined_df)

            # Calculate optimization metrics
            optimization_metrics = self._calculate_optimization_metrics(df, refined_df)

            return {
                "refined_dataset": refined_df.to_dict(),
                "data_quality_report": quality_report,
                "transformation_summary": transformation_summary,
                "optimization_metrics": optimization_metrics,
                "success": True
            }

        except Exception as e:
            return {
                "error": f"Data fining failed: {str(e)}",
                "success": False
            }

    def _assess_initial_quality(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Assess initial data quality"""
        return {
            "missing_values": df.isnull().sum().sum(),
            "duplicate_rows": df.duplicated().sum(),
            "data_types": df.dtypes.to_dict(),
            "memory_usage": df.memory_usage(deep=True).sum(),
            "unique_values": {col: df[col].nunique() for col in df.columns}
        }

    def _apply_data_cleaning(self, df: pd.DataFrame) -> pd.DataFrame:
        """Apply comprehensive data cleaning"""
        cleaned_df = df.copy()
        
        # Remove duplicate rows
        cleaned_df = cleaned_df.drop_duplicates()
        
        # Handle missing values
        for col in cleaned_df.columns:
            if cleaned_df[col].dtype in ['object', 'category']:
                # Fill categorical missing values with mode or 'Unknown'
                mode_value = cleaned_df[col].mode()
                fill_value = mode_value[0] if len(mode_value) > 0 else 'Unknown'
                cleaned_df[col] = cleaned_df[col].fillna(fill_value)
            elif cleaned_df[col].dtype in ['int64', 'float64']:
                # Fill numerical missing values with median
                cleaned_df[col] = cleaned_df[col].fillna(cleaned_df[col].median())
        
        # Standardize column names
        cleaned_df.columns = [col.strip().replace(' ', '_').lower() for col in cleaned_df.columns]
        
        return cleaned_df

    def _apply_data_enhancement(self, df: pd.DataFrame, business_context: Dict[str, Any]) -> pd.DataFrame:
        """Apply business-context-aware data enhancements"""
        enhanced_df = df.copy()
        
        # Detect and convert date columns
        for col in enhanced_df.columns:
            if 'date' in col.lower() or 'time' in col.lower():
                try:
                    enhanced_df[col] = pd.to_datetime(enhanced_df[col], errors='ignore')
                except:
                    pass
        
        # Create derived features based on business context
        industry = business_context.get('industry', '').lower()
        
        if industry in ['retail', 'ecommerce']:
            enhanced_df = self._add_retail_features(enhanced_df)
        elif industry in ['finance', 'financial']:
            enhanced_df = self._add_financial_features(enhanced_df)
        elif industry in ['marketing', 'advertising']:
            enhanced_df = self._add_marketing_features(enhanced_df)
        
        # Add general business features
        enhanced_df = self._add_general_business_features(enhanced_df)
        
        return enhanced_df

    def _add_retail_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add retail-specific derived features"""
        enhanced_df = df.copy()
        
        # Revenue per transaction
        if 'revenue' in enhanced_df.columns and 'quantity' in enhanced_df.columns:
            enhanced_df['revenue_per_unit'] = enhanced_df['revenue'] / enhanced_df['quantity']
        
        # Customer value segments
        if 'revenue' in enhanced_df.columns:
            enhanced_df['revenue_quartile'] = pd.qcut(enhanced_df['revenue'], 4, labels=['Low', 'Medium', 'High', 'Premium'])
        
        return enhanced_df

    def _add_financial_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add finance-specific derived features"""
        enhanced_df = df.copy()
        
        # Profit margins
        if 'revenue' in enhanced_df.columns and 'cost' in enhanced_df.columns:
            enhanced_df['profit_margin'] = (enhanced_df['revenue'] - enhanced_df['cost']) / enhanced_df['revenue']
        
        # Growth rates
        numeric_cols = enhanced_df.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            if len(enhanced_df) > 1:
                enhanced_df[f'{col}_growth_rate'] = enhanced_df[col].pct_change()
        
        return enhanced_df

    def _add_marketing_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add marketing-specific derived features"""
        enhanced_df = df.copy()
        
        # Conversion rates
        if 'conversions' in enhanced_df.columns and 'impressions' in enhanced_df.columns:
            enhanced_df['conversion_rate'] = enhanced_df['conversions'] / enhanced_df['impressions']
        
        # Cost efficiency
        if 'cost' in enhanced_df.columns and 'conversions' in enhanced_df.columns:
            enhanced_df['cost_per_conversion'] = enhanced_df['cost'] / enhanced_df['conversions']
        
        return enhanced_df

    def _add_general_business_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add general business-relevant features"""
        enhanced_df = df.copy()
        
        # Date-based features for any datetime columns
        date_cols = enhanced_df.select_dtypes(include=['datetime64']).columns
        for col in date_cols:
            enhanced_df[f'{col}_year'] = enhanced_df[col].dt.year
            enhanced_df[f'{col}_month'] = enhanced_df[col].dt.month
            enhanced_df[f'{col}_day_of_week'] = enhanced_df[col].dt.day_name()
            enhanced_df[f'{col}_quarter'] = enhanced_df[col].dt.quarter
        
        # Numerical feature interactions
        numeric_cols = enhanced_df.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) >= 2:
            # Create ratio features for first two numeric columns
            col1, col2 = numeric_cols[0], numeric_cols[1]
            if enhanced_df[col2].sum() != 0:
                enhanced_df[f'{col1}_to_{col2}_ratio'] = enhanced_df[col1] / enhanced_df[col2]
        
        return enhanced_df

    def _apply_optimization(self, df: pd.DataFrame, objectives: List[str]) -> pd.DataFrame:
        """Apply optimization based on analysis objectives"""
        optimized_df = df.copy()
        
        # Memory optimization
        optimized_df = self._optimize_memory_usage(optimized_df)
        
        # Feature selection based on objectives
        if 'forecasting' in objectives:
            optimized_df = self._optimize_for_forecasting(optimized_df)
        
        if 'correlation_analysis' in objectives:
            optimized_df = self._optimize_for_correlation(optimized_df)
        
        return optimized_df

    def _optimize_memory_usage(self, df: pd.DataFrame) -> pd.DataFrame:
        """Optimize memory usage of the dataframe"""
        optimized_df = df.copy()
        
        # Optimize integer columns
        for col in optimized_df.select_dtypes(include=['int64']).columns:
            col_min = optimized_df[col].min()
            col_max = optimized_df[col].max()
            
            if col_min >= 0:
                if col_max < 255:
                    optimized_df[col] = optimized_df[col].astype('uint8')
                elif col_max < 65535:
                    optimized_df[col] = optimized_df[col].astype('uint16')
                elif col_max < 4294967295:
                    optimized_df[col] = optimized_df[col].astype('uint32')
            else:
                if col_min > -128 and col_max < 127:
                    optimized_df[col] = optimized_df[col].astype('int8')
                elif col_min > -32768 and col_max < 32767:
                    optimized_df[col] = optimized_df[col].astype('int16')
                elif col_min > -2147483648 and col_max < 2147483647:
                    optimized_df[col] = optimized_df[col].astype('int32')
        
        # Optimize float columns
        for col in optimized_df.select_dtypes(include=['float64']).columns:
            optimized_df[col] = pd.to_numeric(optimized_df[col], downcast='float')
        
        # Optimize object columns
        for col in optimized_df.select_dtypes(include=['object']).columns:
            if optimized_df[col].nunique() / len(optimized_df) < 0.5:
                optimized_df[col] = optimized_df[col].astype('category')
        
        return optimized_df

    def _optimize_for_forecasting(self, df: pd.DataFrame) -> pd.DataFrame:
        """Optimize dataset for forecasting analysis"""
        # Ensure datetime index if datetime columns exist
        date_cols = df.select_dtypes(include=['datetime64']).columns
        if len(date_cols) > 0:
            df_sorted = df.sort_values(date_cols[0])
            return df_sorted
        return df

    def _optimize_for_correlation(self, df: pd.DataFrame) -> pd.DataFrame:
        """Optimize dataset for correlation analysis"""
        # Remove constant columns (zero variance)
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            if df[col].var() == 0:
                df = df.drop(columns=[col])
        return df

    def _generate_quality_report(self, original_df: pd.DataFrame, refined_df: pd.DataFrame, 
                               original_quality: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive data quality report"""
        final_quality = self._assess_initial_quality(refined_df)
        
        improvements = {
            "missing_values_reduced": original_quality["missing_values"] - final_quality["missing_values"],
            "duplicates_removed": original_quality["duplicate_rows"] - final_quality["duplicate_rows"],
            "memory_saved": original_quality["memory_usage"] - final_quality["memory_usage"],
            "features_added": len(refined_df.columns) - len(original_df.columns)
        }
        
        return {
            "original_quality": original_quality,
            "final_quality": final_quality,
            "improvements": improvements,
            "quality_score": self._calculate_quality_score(final_quality)
        }

    def _create_transformation_summary(self, original_df: pd.DataFrame, refined_df: pd.DataFrame) -> Dict[str, Any]:
        """Create summary of all transformations applied"""
        return {
            "original_shape": original_df.shape,
            "final_shape": refined_df.shape,
            "columns_added": [col for col in refined_df.columns if col not in original_df.columns],
            "columns_removed": [col for col in original_df.columns if col not in refined_df.columns],
            "data_types_optimized": len([col for col in refined_df.columns 
                                       if col in original_df.columns and 
                                       refined_df[col].dtype != original_df[col].dtype]),
            "transformations_applied": [
                "duplicate_removal",
                "missing_value_imputation",
                "column_standardization",
                "feature_engineering",
                "memory_optimization"
            ]
        }

    def _calculate_optimization_metrics(self, original_df: pd.DataFrame, refined_df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate optimization performance metrics"""
        original_memory = original_df.memory_usage(deep=True).sum()
        refined_memory = refined_df.memory_usage(deep=True).sum()
        
        return {
            "memory_reduction_percent": ((original_memory - refined_memory) / original_memory * 100),
            "data_completeness_percent": ((1 - refined_df.isnull().sum().sum() / refined_df.size) * 100),
            "feature_enhancement_ratio": len(refined_df.columns) / len(original_df.columns),
            "quality_improvement_score": self._calculate_quality_improvement(original_df, refined_df)
        }

    def _calculate_quality_score(self, quality_metrics: Dict[str, Any]) -> float:
        """Calculate overall data quality score"""
        missing_penalty = quality_metrics["missing_values"] / (quality_metrics.get("total_cells", 1000))
        duplicate_penalty = quality_metrics["duplicate_rows"] / 100  # Assume max 100 for normalization
        
        base_score = 1.0
        quality_score = max(0.0, base_score - missing_penalty - duplicate_penalty)
        
        return min(1.0, quality_score)

    def _calculate_quality_improvement(self, original_df: pd.DataFrame, refined_df: pd.DataFrame) -> float:
        """Calculate quality improvement score"""
        original_missing = original_df.isnull().sum().sum() / original_df.size
        refined_missing = refined_df.isnull().sum().sum() / refined_df.size
        
        original_duplicates = original_df.duplicated().sum() / len(original_df)
        refined_duplicates = refined_df.duplicated().sum() / len(refined_df)
        
        missing_improvement = max(0, original_missing - refined_missing)
        duplicate_improvement = max(0, original_duplicates - refined_duplicates)
        
        return (missing_improvement + duplicate_improvement) / 2
