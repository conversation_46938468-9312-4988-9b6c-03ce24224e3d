"""
ProphetAgent: Performs time series forecasting and predictions as part of the PEER Execute phase.
"""
from agentuniverse.agent.agent import Agent
from typing import Any, Dict, Optional
import pandas as pd
import numpy as np
import json

class ProphetAgent(Agent):
    """Performs time series forecasting using Prophet and trend analysis."""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def input_keys(self) -> list[str]:
        return ["dataset", "target_column", "date_column", "forecast_periods", "analysis_context"]

    def output_keys(self) -> list[str]:
        return ["forecast_results", "trend_analysis", "predictions", "forecast_plots"]

    def parse_input(self, input_object, agent_input):
        return {
            "dataset": agent_input.get("dataset"),
            "target_column": agent_input.get("target_column", ""),
            "date_column": agent_input.get("date_column", ""),
            "forecast_periods": agent_input.get("forecast_periods", 30),
            "analysis_context": agent_input.get("analysis_context", {})
        }

    def parse_result(self, agent_result):
        return agent_result

    def execute(self, input_object, agent_input):
        """
        Perform time series forecasting and trend analysis.
        """
        dataset = agent_input.get("dataset")
        target_column = agent_input.get("target_column", "")
        date_column = agent_input.get("date_column", "")
        forecast_periods = agent_input.get("forecast_periods", 30)
        analysis_context = agent_input.get("analysis_context", {})

        if dataset is None:
            return {"error": "No dataset provided", "success": False}

        try:
            # Convert to DataFrame if needed
            if isinstance(dataset, dict):
                df = pd.DataFrame(dataset)
            elif isinstance(dataset, list):
                df = pd.DataFrame(dataset)
            elif isinstance(dataset, pd.DataFrame):
                df = dataset
            else:
                return {"error": "Unsupported dataset format", "success": False}

            # Auto-detect date and target columns if not provided
            if not date_column:
                date_column = self._detect_date_column(df)
            if not target_column:
                target_column = self._detect_target_column(df)

            if not date_column or not target_column:
                return {
                    "error": "Could not identify date or target columns for forecasting",
                    "success": False,
                    "available_columns": df.columns.tolist()
                }

            # Prepare data for forecasting
            forecast_data = self._prepare_forecast_data(df, date_column, target_column)
            
            if forecast_data.empty:
                return {"error": "No valid data for forecasting", "success": False}

            # Perform trend analysis
            trend_analysis = self._analyze_trends(forecast_data)
            
            # Generate simple predictions (without Prophet for now)
            predictions = self._generate_simple_predictions(forecast_data, forecast_periods)
            
            # Create forecast results
            forecast_results = {
                "data_points": len(forecast_data),
                "forecast_horizon": forecast_periods,
                "date_range": {
                    "start": forecast_data['ds'].min().isoformat(),
                    "end": forecast_data['ds'].max().isoformat()
                },
                "target_column": target_column,
                "date_column": date_column
            }

            return {
                "forecast_results": forecast_results,
                "trend_analysis": trend_analysis,
                "predictions": predictions,
                "forecast_plots": {"placeholder": "Forecast visualization would be generated here"},
                "success": True
            }

        except Exception as e:
            return {
                "error": f"Forecasting failed: {str(e)}",
                "success": False
            }

    def _detect_date_column(self, df: pd.DataFrame) -> Optional[str]:
        """Auto-detect date column"""
        date_keywords = ['date', 'time', 'timestamp', 'day', 'month', 'year', 'created', 'updated']
        
        # Check column names first
        for col in df.columns:
            if any(keyword in col.lower() for keyword in date_keywords):
                try:
                    pd.to_datetime(df[col].head(), errors='raise')
                    return col
                except:
                    continue
        
        # Check data types
        for col in df.columns:
            if df[col].dtype.name.startswith('datetime'):
                return col
            
            # Try to parse as datetime
            try:
                sample = df[col].dropna().head(10)
                if len(sample) > 0:
                    pd.to_datetime(sample, errors='raise')
                    return col
            except:
                continue
        
        return None

    def _detect_target_column(self, df: pd.DataFrame) -> Optional[str]:
        """Auto-detect target column for forecasting"""
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        
        # Prefer columns with names suggesting business metrics
        target_keywords = ['sales', 'revenue', 'profit', 'value', 'amount', 'total', 'count']
        
        for col in numeric_cols:
            if any(keyword in col.lower() for keyword in target_keywords):
                return col
        
        # Return first numeric column if no keyword match
        return numeric_cols[0] if len(numeric_cols) > 0 else None

    def _prepare_forecast_data(self, df: pd.DataFrame, date_column: str, target_column: str) -> pd.DataFrame:
        """Prepare data for forecasting (Prophet format)"""
        forecast_df = df[[date_column, target_column]].copy()
        forecast_df.columns = ['ds', 'y']
        
        # Convert date column
        forecast_df['ds'] = pd.to_datetime(forecast_df['ds'], errors='coerce')
        
        # Remove invalid data
        forecast_df = forecast_df.dropna()
        
        # Sort by date
        forecast_df = forecast_df.sort_values('ds').reset_index(drop=True)
        
        return forecast_df

    def _analyze_trends(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze trends in the time series data"""
        if len(df) < 2:
            return {"trend": "insufficient_data"}
        
        # Simple trend analysis
        first_value = df['y'].iloc[0]
        last_value = df['y'].iloc[-1]
        mean_value = df['y'].mean()
        std_value = df['y'].std()
        
        # Calculate trend direction
        if last_value > first_value * 1.05:
            trend_direction = "increasing"
        elif last_value < first_value * 0.95:
            trend_direction = "decreasing"
        else:
            trend_direction = "stable"
        
        # Calculate growth rate
        growth_rate = ((last_value - first_value) / first_value * 100) if first_value != 0 else 0
        
        return {
            "trend_direction": trend_direction,
            "growth_rate": float(growth_rate),
            "mean_value": float(mean_value),
            "std_deviation": float(std_value),
            "volatility": float(std_value / mean_value * 100) if mean_value != 0 else 0,
            "data_points": len(df)
        }

    def _generate_simple_predictions(self, df: pd.DataFrame, periods: int) -> Dict[str, Any]:
        """Generate simple linear trend predictions"""
        if len(df) < 2:
            return {"error": "Insufficient data for predictions"}
        
        # Simple linear trend
        x = np.arange(len(df))
        y = df['y'].values
        
        # Linear regression
        coeffs = np.polyfit(x, y, 1)
        slope, intercept = coeffs
        
        # Generate future predictions
        future_x = np.arange(len(df), len(df) + periods)
        future_predictions = slope * future_x + intercept
        
        # Generate future dates
        last_date = df['ds'].iloc[-1]
        date_freq = self._infer_frequency(df['ds'])
        future_dates = pd.date_range(start=last_date, periods=periods + 1, freq=date_freq)[1:]
        
        predictions = []
        for i, (date, pred) in enumerate(zip(future_dates, future_predictions)):
            predictions.append({
                "date": date.isoformat(),
                "predicted_value": float(pred),
                "period": i + 1
            })
        
        return {
            "predictions": predictions,
            "trend_slope": float(slope),
            "trend_intercept": float(intercept),
            "forecast_method": "linear_trend"
        }

    def _infer_frequency(self, dates: pd.Series) -> str:
        """Infer the frequency of the time series"""
        if len(dates) < 2:
            return 'D'  # Default to daily
        
        diff = dates.diff().mode()
        if len(diff) == 0:
            return 'D'
        
        days = diff.iloc[0].days
        
        if days == 1:
            return 'D'  # Daily
        elif days == 7:
            return 'W'  # Weekly
        elif 28 <= days <= 31:
            return 'M'  # Monthly
        elif 365 <= days <= 366:
            return 'Y'  # Yearly
        else:
            return 'D'  # Default to daily
