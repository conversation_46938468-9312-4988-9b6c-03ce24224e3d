# AI Data Analysis Platform

Revolutionary AI Data Analysis Platform using AgentUniverse framework with DeepSeek LLMs. This platform employs a multi-agent architecture using PEER (Plan/Execute/Express/Review) and DOE (Data-fining/Opinion-inject/Express) collaboration patterns to deliver comprehensive business intelligence analysis.

## 🚀 Features

- **Multi-Agent Architecture**: PEER + DOE patterns for comprehensive analysis
- **DeepSeek LLM Integration**: Utilizes DeepSeek V3 and DeepSeek R1 models
- **Comprehensive Business Intelligence**: From data analysis to strategic recommendations
- **AgentUniverse Framework**: Built on robust agent orchestration platform
- **Real-time Analysis**: Upload files and get immediate insights
- **Executive-Ready Reports**: Professional business intelligence narratives

## 🏗️ Architecture

### PEER Pattern Agents
- **PlannerAgent**: Strategic analysis planning (PEER Plan phase)
- **ExecutorAgents**: Data analysis, KPI discovery, forecasting, code execution (PEER Execute phase)
- **ExpresserAgent**: Initial results expression (PEER Express phase)
- **ReviewerAgent**: Quality assessment and validation (PEER Review phase)

### DOE Pattern Agents
- **DataFiningAgent**: Data refinement and optimization (DOE Data-fining phase)
- **OpinionInjectAgent**: Business expertise injection (DOE Opinion-inject phase)
- **StorytellerAgent**: Business intelligence report generation (DOE Express phase)

### Master Controller
- **AgentUniverseOrchestrator**: Coordinates the complete agent swarm workflow

## 📋 Requirements

- Python 3.9 or higher
- DeepSeek API key
- 8GB+ RAM recommended
- 2GB+ storage space

## 🛠️ Installation

### Option 1: Quick Install (Recommended)

```bash
# Clone the repository
git clone https://github.com/your-org/ai-data-analysis-platform.git
cd ai-data-analysis-platform

# Run the installation script
python install.py
```

### Option 2: Manual Installation

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Or install with optional dependencies
pip install -e ".[all]"
```

### Option 3: Development Installation

```bash
# Install with development dependencies
pip install -e ".[dev]"

# Install pre-commit hooks
pre-commit install

# Run tests
pytest
```

## ⚙️ Configuration

1. **DeepSeek API Key**: Create a `custom_key.toml` file:
```toml
[deepseek]
api_key = "your-deepseek-api-key-here"
base_url = "https://api.deepseek.com/v1"
```

2. **Agent Configuration**: All agent configurations are in the `agents/` directory with corresponding YAML files.

## 🚀 Quick Start

### 1. Validate Installation
```bash
python validate_agents.py
```

### 2. Run Basic Analysis
```python
from agents.orchestrator.agent_universe_orchestrator.agent_universe_orchestrator import AgentUniverseOrchestrator

# Initialize orchestrator
orchestrator = AgentUniverseOrchestrator()

# Run analysis
results = orchestrator.execute(
    input_object={},
    agent_input={
        "user_query": "Analyze our sales performance",
        "file_data": {"type": "csv", "path": "data/sales.csv"},
        "business_context": "Retail business"
    }
)

print(results["final_business_intelligence_report"])
```

### 3. Access Web Interface (if using Streamlit)
```bash
streamlit run app.py
```

## 📊 Usage Examples

### Business Intelligence Analysis
```python
# Upload your data file and get comprehensive analysis
orchestrator.analyze_business_data(
    file_path="data/business_data.xlsx",
    analysis_type="comprehensive",
    target_audience="executives"
)
```

### KPI Discovery
```python
# Discover relevant KPIs for your business
kpi_agent = KPIDiscoveryAgent()
kpis = kpi_agent.discover_kpis(
    data=your_data,
    business_type="retail",
    objectives=["growth", "efficiency"]
)
```

### Forecasting
```python
# Generate forecasts for key metrics
prophet_agent = ProphetAgent()
forecasts = prophet_agent.generate_forecasts(
    data=time_series_data,
    periods=12,
    confidence_intervals=True
)
```

## 🧪 Testing

```bash
# Run all tests
pytest

# Run specific test categories
pytest -m unit           # Unit tests only
pytest -m integration    # Integration tests only
pytest -m "not slow"     # Skip slow tests

# Run with coverage
pytest --cov=agents --cov-report=html
```

## 📁 Project Structure

```
ai-data-analysis-platform/
├── agents/                          # Agent implementations
│   ├── core_analysis/               # PEER pattern agents
│   │   ├── planner_agent/
│   │   ├── executor_agent/
│   │   │   ├── data_analyzer_agent/
│   │   │   ├── kpi_discovery_agent/
│   │   │   ├── prophet_agent/
│   │   │   └── code_executor_agent/
│   │   ├── expresser_agent/
│   │   └── reviewer_agent/
│   ├── specialized_business/        # DOE pattern agents
│   │   ├── data_fining_agent/
│   │   ├── opinion_inject_agent/
│   │   └── storyteller_agent/
│   └── orchestrator/                # Master controller
│       └── agent_universe_orchestrator/
├── scripts/                         # Utility scripts
├── tests/                          # Test suite
├── docs/                           # Documentation
├── requirements.txt                # Dependencies
├── pyproject.toml                 # Project configuration
├── setup.py                       # Setup script
└── README.md                      # This file
```

## 🔧 Configuration

### Environment Variables
```bash
DEEPSEEK_API_KEY=your-api-key
DEEPSEEK_BASE_URL=https://api.deepseek.com/v1
LOG_LEVEL=INFO
CACHE_ENABLED=true
```

### Agent Configuration
Each agent has a corresponding YAML configuration file for:
- LLM model selection (deepseek-v3, deepseek-r1)
- Prompt templates
- Input/output specifications
- Performance parameters

## 📈 Performance

- **Average Analysis Time**: 3-5 minutes for comprehensive reports
- **Supported File Sizes**: Up to 100MB datasets
- **Concurrent Users**: Scales with infrastructure
- **Memory Usage**: 2-8GB depending on data size

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [Read the Docs](https://ai-data-analysis-platform.readthedocs.io/)
- **Issues**: [GitHub Issues](https://github.com/your-org/ai-data-analysis-platform/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-org/ai-data-analysis-platform/discussions)
- **Email**: <EMAIL>

## 🙏 Acknowledgments

- [AgentUniverse](https://github.com/antgroup/agentUniverse) for the agent framework
- [DeepSeek](https://deepseek.com) for powerful LLM capabilities
- The open-source community for inspiration and tools

## 🗺️ Roadmap

- [ ] Web UI with drag-and-drop file upload
- [ ] Real-time collaboration features
- [ ] Advanced visualization dashboards
- [ ] Integration with cloud storage (AWS S3, Azure Blob, GCP)
- [ ] Enterprise SSO integration
- [ ] Custom agent template creator
- [ ] Multi-language support
- [ ] Mobile application

---

**Built with ❤️ by the AI Data Analysis Team**
