"""
ReviewerAgent: Reviews and validates analysis results as part of the PEER Review phase.
"""
from agentuniverse.agent.agent import Agent
from typing import Any, Dict, List
import json

class ReviewerAgent(Agent):
    """Reviews analysis results for accuracy, completeness, and business relevance."""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def input_keys(self) -> list[str]:
        return ["analysis_results", "original_requirements", "quality_criteria", "business_context"]

    def output_keys(self) -> list[str]:
        return ["review_summary", "quality_assessment", "recommendations", "approval_status"]

    def parse_input(self, input_object, agent_input):
        return {
            "analysis_results": agent_input.get("analysis_results", {}),
            "original_requirements": agent_input.get("original_requirements", {}),
            "quality_criteria": agent_input.get("quality_criteria", {}),
            "business_context": agent_input.get("business_context", {})
        }

    def parse_result(self, agent_result):
        return agent_result

    def execute(self, input_object, agent_input):
        """
        Review analysis results for quality, accuracy, and business alignment.
        """
        analysis_results = agent_input.get("analysis_results", {})
        original_requirements = agent_input.get("original_requirements", {})
        quality_criteria = agent_input.get("quality_criteria", {})
        business_context = agent_input.get("business_context", {})

        if not analysis_results:
            return {"error": "No analysis results provided for review", "success": False}

        try:
            # Perform quality assessment
            quality_assessment = self._assess_quality(analysis_results, quality_criteria)
            
            # Check completeness against requirements
            completeness_check = self._check_completeness(analysis_results, original_requirements)
            
            # Validate business alignment
            business_alignment = self._validate_business_alignment(analysis_results, business_context)
            
            # Assess data accuracy and consistency
            accuracy_assessment = self._assess_accuracy(analysis_results)
            
            # Generate overall review summary
            review_summary = self._generate_review_summary(
                quality_assessment, completeness_check, business_alignment, accuracy_assessment
            )
            
            # Provide recommendations for improvement
            recommendations = self._generate_recommendations(
                quality_assessment, completeness_check, business_alignment
            )
            
            # Determine approval status
            approval_status = self._determine_approval_status(
                quality_assessment, completeness_check, business_alignment
            )

            return {
                "review_summary": review_summary,
                "quality_assessment": quality_assessment,
                "recommendations": recommendations,
                "approval_status": approval_status,
                "success": True
            }

        except Exception as e:
            return {
                "error": f"Review failed: {str(e)}",
                "success": False
            }

    def _assess_quality(self, results: Dict[str, Any], criteria: Dict[str, Any]) -> Dict[str, Any]:
        """Assess the quality of analysis results"""
        quality_scores = {}
        overall_quality = "good"
        issues = []

        # Check data completeness
        data_completeness = self._check_data_completeness(results)
        quality_scores["data_completeness"] = data_completeness
        if data_completeness < 0.8:
            issues.append("Incomplete data in analysis results")
            overall_quality = "needs_improvement"

        # Check result consistency
        consistency_score = self._check_consistency(results)
        quality_scores["consistency"] = consistency_score
        if consistency_score < 0.7:
            issues.append("Inconsistent results detected")
            overall_quality = "needs_improvement"

        # Check statistical validity
        statistical_validity = self._check_statistical_validity(results)
        quality_scores["statistical_validity"] = statistical_validity
        if statistical_validity < 0.6:
            issues.append("Statistical validity concerns")
            overall_quality = "poor"

        return {
            "overall_quality": overall_quality,
            "quality_scores": quality_scores,
            "issues_identified": issues,
            "quality_metrics": {
                "data_completeness": f"{data_completeness:.2%}",
                "consistency": f"{consistency_score:.2%}",
                "statistical_validity": f"{statistical_validity:.2%}"
            }
        }

    def _check_completeness(self, results: Dict[str, Any], requirements: Dict[str, Any]) -> Dict[str, Any]:
        """Check if analysis results meet original requirements"""
        required_outputs = requirements.get("expected_outputs", [])
        provided_outputs = list(results.keys())
        
        missing_outputs = [output for output in required_outputs if output not in provided_outputs]
        completeness_score = 1.0 - (len(missing_outputs) / len(required_outputs)) if required_outputs else 1.0

        return {
            "completeness_score": completeness_score,
            "required_outputs": required_outputs,
            "provided_outputs": provided_outputs,
            "missing_outputs": missing_outputs,
            "status": "complete" if completeness_score >= 0.9 else "incomplete"
        }

    def _validate_business_alignment(self, results: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Validate alignment with business context and objectives"""
        business_objectives = context.get("objectives", [])
        industry = context.get("industry", "general")
        
        alignment_score = 0.8  # Default good alignment
        alignment_issues = []

        # Check if KPIs align with business objectives
        if "discovered_kpis" in results:
            kpi_data = results["discovered_kpis"]
            if not isinstance(kpi_data, dict) or len(kpi_data) == 0:
                alignment_issues.append("No meaningful KPIs discovered")
                alignment_score -= 0.2

        # Check if analysis addresses business questions
        if "business_insights" in results:
            insights = results["business_insights"]
            if not insights or len(insights) == 0:
                alignment_issues.append("Limited business insights provided")
                alignment_score -= 0.1

        # Industry-specific validation
        if industry in ["retail", "ecommerce"] and "discovered_kpis" in results:
            kpi_names = list(results["discovered_kpis"].keys())
            retail_kpis = ["revenue", "sales", "conversion", "customer"]
            if not any(any(retail_kpi in kpi_name.lower() for retail_kpi in retail_kpis) for kpi_name in kpi_names):
                alignment_issues.append("Missing retail-specific KPIs")
                alignment_score -= 0.1

        return {
            "alignment_score": max(0.0, alignment_score),
            "business_objectives": business_objectives,
            "industry": industry,
            "alignment_issues": alignment_issues,
            "status": "aligned" if alignment_score >= 0.7 else "misaligned"
        }

    def _assess_accuracy(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Assess data accuracy and calculation consistency"""
        accuracy_issues = []
        accuracy_score = 1.0

        # Check for null/invalid values in KPI calculations
        if "discovered_kpis" in results:
            kpi_data = results["discovered_kpis"]
            if isinstance(kpi_data, dict):
                for kpi_name, kpi_info in kpi_data.items():
                    if isinstance(kpi_info, dict) and "value" in kpi_info:
                        value = kpi_info["value"]
                        if value is None or (isinstance(value, float) and (value != value or value == float('inf'))):  # Check for NaN or inf
                            accuracy_issues.append(f"Invalid value in KPI: {kpi_name}")
                            accuracy_score -= 0.1

        # Check forecast accuracy indicators
        if "forecast_results" in results:
            forecast_data = results["forecast_results"]
            if isinstance(forecast_data, dict) and "data_points" in forecast_data:
                data_points = forecast_data["data_points"]
                if data_points < 10:
                    accuracy_issues.append("Insufficient data points for reliable forecasting")
                    accuracy_score -= 0.2

        # Check for statistical consistency
        if "trend_analysis" in results:
            trend_data = results["trend_analysis"]
            if isinstance(trend_data, dict):
                growth_rate = trend_data.get("growth_rate")
                if growth_rate is not None and abs(growth_rate) > 1000:  # Unrealistic growth rate
                    accuracy_issues.append("Unrealistic growth rate detected")
                    accuracy_score -= 0.2

        return {
            "accuracy_score": max(0.0, accuracy_score),
            "accuracy_issues": accuracy_issues,
            "status": "accurate" if accuracy_score >= 0.8 else "inaccurate"
        }

    def _generate_review_summary(self, quality_assessment: Dict, completeness_check: Dict, 
                                business_alignment: Dict, accuracy_assessment: Dict) -> Dict[str, Any]:
        """Generate comprehensive review summary"""
        overall_score = (
            quality_assessment["quality_scores"].get("data_completeness", 0) * 0.25 +
            completeness_check["completeness_score"] * 0.25 +
            business_alignment["alignment_score"] * 0.25 +
            accuracy_assessment["accuracy_score"] * 0.25
        )

        if overall_score >= 0.8:
            overall_status = "excellent"
        elif overall_score >= 0.6:
            overall_status = "good"
        elif overall_score >= 0.4:
            overall_status = "needs_improvement"
        else:
            overall_status = "poor"

        key_findings = []
        
        # Quality findings
        if quality_assessment["overall_quality"] != "good":
            key_findings.append(f"Quality issues: {quality_assessment['overall_quality']}")
        
        # Completeness findings
        if completeness_check["status"] == "incomplete":
            key_findings.append(f"Missing outputs: {len(completeness_check['missing_outputs'])}")
        
        # Business alignment findings
        if business_alignment["status"] == "misaligned":
            key_findings.append("Business alignment concerns")
        
        # Accuracy findings
        if accuracy_assessment["status"] == "inaccurate":
            key_findings.append("Data accuracy issues detected")

        return {
            "overall_score": overall_score,
            "overall_status": overall_status,
            "key_findings": key_findings,
            "detailed_scores": {
                "quality": quality_assessment["quality_scores"].get("data_completeness", 0),
                "completeness": completeness_check["completeness_score"],
                "business_alignment": business_alignment["alignment_score"],
                "accuracy": accuracy_assessment["accuracy_score"]
            }
        }

    def _generate_recommendations(self, quality_assessment: Dict, completeness_check: Dict, 
                                business_alignment: Dict) -> List[str]:
        """Generate recommendations for improvement"""
        recommendations = []

        # Quality improvement recommendations
        for issue in quality_assessment.get("issues_identified", []):
            if "Incomplete data" in issue:
                recommendations.append("Improve data collection and validation processes")
            elif "Inconsistent results" in issue:
                recommendations.append("Review calculation methodologies for consistency")
            elif "Statistical validity" in issue:
                recommendations.append("Enhance statistical analysis techniques and validation")

        # Completeness recommendations
        if completeness_check["status"] == "incomplete":
            missing = completeness_check["missing_outputs"]
            recommendations.append(f"Complete missing analysis outputs: {', '.join(missing)}")

        # Business alignment recommendations
        for issue in business_alignment.get("alignment_issues", []):
            if "No meaningful KPIs" in issue:
                recommendations.append("Develop industry-specific KPI discovery methods")
            elif "Limited business insights" in issue:
                recommendations.append("Enhance business insight generation capabilities")

        # Default recommendations if none specific
        if not recommendations:
            recommendations = [
                "Continue monitoring analysis quality",
                "Regular validation of business alignment",
                "Maintain current high standards"
            ]

        return recommendations

    def _determine_approval_status(self, quality_assessment: Dict, completeness_check: Dict, 
                                 business_alignment: Dict) -> Dict[str, Any]:
        """Determine overall approval status"""
        
        # Check critical failures
        critical_failures = []
        
        if quality_assessment["overall_quality"] == "poor":
            critical_failures.append("Poor quality assessment")
        
        if completeness_check["completeness_score"] < 0.5:
            critical_failures.append("Severely incomplete analysis")
        
        if business_alignment["alignment_score"] < 0.4:
            critical_failures.append("Poor business alignment")

        # Determine status
        if critical_failures:
            status = "rejected"
            message = f"Analysis rejected due to: {'; '.join(critical_failures)}"
        elif (quality_assessment["overall_quality"] == "good" and 
              completeness_check["completeness_score"] >= 0.8 and 
              business_alignment["alignment_score"] >= 0.7):
            status = "approved"
            message = "Analysis meets all quality standards and requirements"
        else:
            status = "conditional_approval"
            message = "Analysis approved with recommendations for improvement"

        return {
            "status": status,
            "message": message,
            "critical_failures": critical_failures,
            "requires_revision": status == "rejected"
        }

    def _check_data_completeness(self, results: Dict[str, Any]) -> float:
        """Check data completeness score"""
        total_expected = 10  # Expected number of result categories
        provided = len([v for v in results.values() if v is not None and v != {} and v != []])
        return min(1.0, provided / total_expected)

    def _check_consistency(self, results: Dict[str, Any]) -> float:
        """Check result consistency"""
        # Simple consistency check - in real implementation would be more sophisticated
        consistency_score = 0.8
        
        # Check if KPI values are reasonable
        if "discovered_kpis" in results:
            kpi_data = results["discovered_kpis"]
            if isinstance(kpi_data, dict):
                for kpi_info in kpi_data.values():
                    if isinstance(kpi_info, dict) and "value" in kpi_info:
                        value = kpi_info["value"]
                        if isinstance(value, (int, float)) and value < 0:
                            consistency_score -= 0.1  # Negative values might be inconsistent
        
        return max(0.0, consistency_score)

    def _check_statistical_validity(self, results: Dict[str, Any]) -> float:
        """Check statistical validity of results"""
        validity_score = 0.7  # Default moderate validity
        
        # Check if trend analysis has sufficient data
        if "trend_analysis" in results:
            trend_data = results["trend_analysis"]
            if isinstance(trend_data, dict) and "data_points" in trend_data:
                data_points = trend_data["data_points"]
                if data_points >= 30:
                    validity_score += 0.2
                elif data_points >= 10:
                    validity_score += 0.1
        
        # Check forecast validity
        if "predictions" in results:
            predictions = results["predictions"]
            if isinstance(predictions, dict) and "predictions" in predictions:
                pred_list = predictions["predictions"]
                if isinstance(pred_list, list) and len(pred_list) > 0:
                    validity_score += 0.1
        
        return min(1.0, validity_score)
