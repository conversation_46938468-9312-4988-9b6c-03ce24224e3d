"""
PlannerAgent: Creates comprehensive analysis plans as part of the PEER Plan phase.
"""
from agentuniverse.agent.agent import Agent
from typing import Any, Dict
import json

class PlannerAgent(Agent):
    """Creates strategic analysis plans based on business context and user requirements."""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def input_keys(self) -> list[str]:
        return ["user_query", "data_summary", "business_context", "available_agents"]

    def output_keys(self) -> list[str]:
        return ["analysis_plan", "execution_strategy", "agent_sequence", "expected_outcomes"]

    def parse_input(self, input_object, agent_input):
        return {
            "user_query": agent_input.get("user_query", ""),
            "data_summary": agent_input.get("data_summary", {}),
            "business_context": agent_input.get("business_context", {}),
            "available_agents": agent_input.get("available_agents", [])
        }

    def parse_result(self, agent_result):
        return agent_result

    def execute(self, input_object, agent_input):
        """
        Create a comprehensive analysis plan based on user requirements and context.
        """
        user_query = agent_input.get("user_query", "")
        data_summary = agent_input.get("data_summary", {})
        business_context = agent_input.get("business_context", {})
        available_agents = agent_input.get("available_agents", [])

        if not user_query:
            return {"error": "No user query provided", "success": False}

        try:
            # Analyze query intent
            analysis_goals = self._analyze_query_intent(user_query)
            
            # Create execution strategy
            execution_strategy = self._create_execution_strategy(analysis_goals, available_agents)
            
            # Plan agent sequence
            agent_sequence = self._plan_agent_sequence(execution_strategy)
            
            # Define expected outcomes
            expected_outcomes = self._define_expected_outcomes(analysis_goals, data_summary)
            
            # Create comprehensive analysis plan
            analysis_plan = {
                "query": user_query,
                "analysis_goals": analysis_goals,
                "data_strategy": self._create_data_strategy(data_summary),
                "business_alignment": self._align_with_business_context(business_context),
                "success_metrics": self._define_success_metrics(analysis_goals)
            }

            return {
                "analysis_plan": analysis_plan,
                "execution_strategy": execution_strategy,
                "agent_sequence": agent_sequence,
                "expected_outcomes": expected_outcomes,
                "success": True
            }

        except Exception as e:
            return {
                "error": f"Planning failed: {str(e)}",
                "success": False
            }

    def _analyze_query_intent(self, query: str) -> Dict[str, Any]:
        """Analyze user query to understand analysis intent"""
        query_lower = query.lower()
        goals = []
        
        # Forecasting intent
        if any(word in query_lower for word in ["forecast", "predict", "future", "trend"]):
            goals.append("forecasting")
        
        # KPI analysis intent
        if any(word in query_lower for word in ["kpi", "metric", "performance", "indicator"]):
            goals.append("kpi_analysis")
        
        # Statistical analysis intent
        if any(word in query_lower for word in ["correlation", "statistics", "analyze", "pattern"]):
            goals.append("statistical_analysis")
        
        # Visualization intent
        if any(word in query_lower for word in ["chart", "graph", "plot", "visualize", "show"]):
            goals.append("visualization")
        
        # Default to comprehensive analysis if no specific intent
        if not goals:
            goals = ["comprehensive_analysis"]
        
        return {
            "primary_goals": goals,
            "complexity": "high" if len(goals) > 2 else "medium" if len(goals) > 1 else "low",
            "query_type": "business_intelligence"
        }

    def _create_execution_strategy(self, analysis_goals: Dict[str, Any], available_agents: list) -> Dict[str, Any]:
        """Create execution strategy based on goals and available agents"""
        strategy = {
            "approach": "sequential_with_parallel",
            "phases": [],
            "estimated_duration": "medium"
        }
        
        primary_goals = analysis_goals.get("primary_goals", [])
        
        # Phase 1: Data Analysis and KPI Discovery (can run in parallel)
        phase_1_agents = []
        if "kpi_analysis" in primary_goals or "comprehensive_analysis" in primary_goals:
            phase_1_agents.append("KPIDiscoveryAgent")
        if "statistical_analysis" in primary_goals or "comprehensive_analysis" in primary_goals:
            phase_1_agents.append("DataAnalyzerAgent")
        
        if phase_1_agents:
            strategy["phases"].append({
                "phase": 1,
                "name": "Data Analysis and KPI Discovery",
                "agents": phase_1_agents,
                "execution": "parallel",
                "dependencies": []
            })
        
        # Phase 2: Forecasting (depends on Phase 1)
        if "forecasting" in primary_goals:
            strategy["phases"].append({
                "phase": 2,
                "name": "Forecasting and Predictions",
                "agents": ["ProphetAgent"],
                "execution": "sequential",
                "dependencies": [1]
            })
        
        # Phase 3: Code Generation and Visualization
        if "visualization" in primary_goals or "comprehensive_analysis" in primary_goals:
            strategy["phases"].append({
                "phase": 3,
                "name": "Code Generation and Visualization",
                "agents": ["CodeExecutorAgent"],
                "execution": "sequential",
                "dependencies": list(range(1, len(strategy["phases"]) + 1))
            })
        
        return strategy

    def _plan_agent_sequence(self, execution_strategy: Dict[str, Any]) -> list:
        """Plan the sequence of agent execution"""
        sequence = []
        
        for phase in execution_strategy.get("phases", []):
            phase_entry = {
                "phase": phase["phase"],
                "phase_name": phase["name"],
                "agents": phase["agents"],
                "execution_type": phase["execution"],
                "dependencies": phase.get("dependencies", [])
            }
            sequence.append(phase_entry)
        
        return sequence

    def _define_expected_outcomes(self, analysis_goals: Dict[str, Any], data_summary: Dict[str, Any]) -> Dict[str, Any]:
        """Define what outcomes are expected from the analysis"""
        outcomes = {
            "deliverables": [],
            "insights": [],
            "artifacts": []
        }
        
        primary_goals = analysis_goals.get("primary_goals", [])
        
        if "kpi_analysis" in primary_goals:
            outcomes["deliverables"].append("KPI Dashboard")
            outcomes["insights"].append("Business Performance Metrics")
            outcomes["artifacts"].append("KPI Calculations")
        
        if "forecasting" in primary_goals:
            outcomes["deliverables"].append("Forecast Charts")
            outcomes["insights"].append("Future Trends Prediction")
            outcomes["artifacts"].append("Forecast Models")
        
        if "statistical_analysis" in primary_goals:
            outcomes["deliverables"].append("Statistical Reports")
            outcomes["insights"].append("Data Patterns and Correlations")
            outcomes["artifacts"].append("Statistical Tests Results")
        
        if "visualization" in primary_goals:
            outcomes["deliverables"].append("Interactive Charts")
            outcomes["artifacts"].append("Visualization Code")
        
        return outcomes

    def _create_data_strategy(self, data_summary: Dict[str, Any]) -> Dict[str, Any]:
        """Create data processing strategy"""
        return {
            "preprocessing_required": True,
            "quality_checks": ["missing_values", "outliers", "data_types"],
            "transformations": ["normalization", "aggregation"],
            "validation": "automated"
        }

    def _align_with_business_context(self, business_context: Dict[str, Any]) -> Dict[str, Any]:
        """Align analysis with business context"""
        return {
            "business_focus": business_context.get("industry", "general"),
            "key_metrics": business_context.get("key_metrics", []),
            "reporting_frequency": business_context.get("reporting_frequency", "monthly")
        }

    def _define_success_metrics(self, analysis_goals: Dict[str, Any]) -> Dict[str, Any]:
        """Define metrics for measuring analysis success"""
        return {
            "completeness": "All planned analyses executed",
            "accuracy": "Results validated and cross-checked",
            "relevance": "Insights aligned with business goals",
            "actionability": "Recommendations are implementable"
        }
