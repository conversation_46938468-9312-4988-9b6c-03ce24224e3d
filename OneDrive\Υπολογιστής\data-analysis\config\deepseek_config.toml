# DeepSeek API Configuration
# Get your real API key from: https://platform.deepseek.com/

[deepseek]
api_key = "***********************************"  # 🔑 REPLACE WITH YOUR ACTUAL API KEY
base_url = "https://api.deepseek.com"
default_model = "deepseek-chat"

# 📝 INSTRUCTIONS:
# 1. Go to https://platform.deepseek.com/
# 2. Get your API key
# 3. Replace "your-real-deepseek-api-key-here" with your actual key
# 4. Save this file
# 5. Restart the web interface

[deepseek.models]
# Primary model for complex reasoning and business analysis
primary = "deepseek-chat"
# Reasoning-focused model for analytical tasks  
reasoning = "deepseek-reasoner"
# Efficient model for simple tasks
efficient = "deepseek-chat"

[deepseek.endpoints]
chat = "/v1/chat/completions"
models = "/v1/models"

[deepseek.settings]
max_tokens = 4096
temperature = 0.1
top_p = 0.9
timeout = 120  # Increased timeout to 2 minutes
connect_timeout = 30
read_timeout = 120
max_retries = 3
