What is agentUniverse?
agentUniverse is a multi-agent framework based on large language models. It provides flexible and easily extensible capabilities for building individual agents. The core of agentUniverse is a rich set of multi-agent collaborative pattern components (serving as a collaborative pattern factory), which allows agents to perform their respective duties and maximize their capabilities when solving problems in different fields; at the same time, agentUniverse focuses on the integration of domain experience, helping you smoothly integrate domain experience into the work of intelligent agents.🎉🎉🎉

🌈🌈🌈agentUniverse originates from the real-world financial business practices of AntGroup (https://github.com/antgroup), dedicated to assisting developers and enterprises in effortlessly constructing domain-expert-level intelligent agents that collaborate to accomplish tasks.



We look forward to your practice and communication and sharing of Patterns in different fields through the community. This framework has already placed many useful components that have been tested in real business scenarios in terms of multi-agent cooperation, and will continue to be enriched in the future. The pattern components that are currently open for use include:

PEER pattern component: This pattern uses agents with different responsibilities—Plan, Execute, Express, and Review—to break down complex problems into manageable steps, execute the steps in sequence, and iteratively improve based on feedback, enhancing the performance of reasoning and analysis tasks. Typical use cases: Event interpretation, industry analysis.
DOE pattern component: This pattern employs three agents—Data-fining, Opinion-inject, and Express—to improve the effectiveness of tasks that are data-intensive, require high computational precision, and incorporate expert opinions. Typical use cases: Financial report generation.
More patterns are coming soon...

The LLM model integration can be accomplished with simple configuration, currently agentUniverse supported models include:

-	Vendors	Models
	Qwen	new qwen3 Series（qwen3-235b-a22b、qwen3-32b、qwen3-30b-a3b, etc.）、qwen2.5-72b-instruct、qwq-32b-preview、qwen-max、…
	Deepseek	deepseek-r1、deepseek-v3、deepseek-r1-distill-qwen-32b、…
	OpenAI	GPT-4o、GPT-4o mini、OpenAI o1、OpenAI o3-mini、…
	Claude	Claude 3.5 Sonnet、Claude 3 Opus、…
	Gemini	Gemini 2.0 Flash、Gemini 2.0 Flash Thinking、Gemini 1.5 Pro、…
	Llama	llama3.3-70b-instruct、llama3.2-3b-instruct、llama3.2-1b-instruct、…
	KIMI	moonshot-v1-128k、moonshot-v1-32k、moonshot-v1-8k、…
	WenXin	ERNIE 4.0、ERNIE 4.0 Turbo、ERNIE 3.5、…
	chatglm	chatglm3-6b、chatglm-6b-v2、…
	BaiChuan	baichuan2-turbo、baichuan2-13b-chat-v1、…
	Doubao	Doubao-pro-128k、Doubao-pro-32k、Doubao-lite-128k、…
For example, to use deepseek model, you can simply set DEEPSEEK_API_KEY value in the custom_key.toml file, and set the llm_model name in the agent configuration file to 'default_deepseek_llm' and you're all set. For more information about llm configuration, please refer to switch-the-llm.

Citation
The agentUniverse project is supported by the following research achievements.

BibTeX formatted

@misc{wang2024peerexpertizingdomainspecifictasks,
      title={PEER: Expertizing Domain-Specific Tasks with a Multi-Agent Framework and Tuning Methods}, 
      author={Yiying Wang and Xiaojing Li and Binzhu Wang and Yueyang Zhou and Han Ji and Hong Chen and Jinshi Zhang and Fei Yu and Zewei Zhao and Song Jin and Renji Gong and Wanqing Xu},
      year={2024},
      eprint={2407.06985},
      archivePrefix={arXiv},
      primaryClass={cs.AI},
      url={https://arxiv.org/abs/2407.06985}, 
}
Overview: This document introduces in detailed the mechanisms and principles underlying the PEER multi-agent framework. The experimental section assigned scores across seven dimensions: completeness, relevance, conciseness, factualness, logicality, structure, and comprehensiveness, with a maximum score of 5 points for each dimension. On average, the PEER model scored higher in each evaluation dimension compared to BabyAGI, and show notable advantages particularly in completeness, relevance, logicality, structure, and comprehensiveness. Furthermore, when tested with the GPT-3.5 Turbo (16k) model, the PEER model achieved a superior accuracy rate of 83% compared to BabyAGI, and with the GPT-4 model, it achieved an accuracy rate of 81%. For more details, please refer to the document. 🔗https://arxiv.org/pdf/2407.06985

Table of Contents
Quick Start
How to build an agent application
Setup the visual agentic workflow platform
Why use agentUniverse
Sample Apps
Documents
Support
Quick Start
Installation
Using pip:

pip install agentUniverse
Run the first example
Run your first example, and you can quickly experience the performance of the agents (or agent groups) built by agentUniverse through the tutorial.

Please refer to the document for detail steps: Run the first example 。

How to build an agent application
Standard Project Scaffolding
Setup the standard project: agentUniverse Standard Project

Quick Start for Building a Single Agent
You can learn how to quickly build a single agent by reading the Quick Guide to Build Single Agent. This will help you understand how to enhance your agent's capabilities using tools, knowledge bases, RAG technologies, and more. Additionally, you will grasp the basic application development processes for agents, including configuration, testing, optimizing, deployment, and performance evaluation.

Building Typical Multi-Agent Applications
You can further understand how to break down intelligent capabilities into multiple agents in complex task scenarios and enhance your task performance through collaboration by referring to the Building Typical Multi-Agent App chapter.

Creating and Using Agent Templates
You can learn how to create effective agent patterns into templates through the chapter Creating and Using Agent Templates. This will greatly enhance the efficiency of constructing subsequent agents and facilitate dissemination.

Using and Publishing MCP Servers
You can learn how to quickly use or publish MCP servers in agentUniverse framework by referring to How to Use MCP Servers and How to Publish MCP Servers.

Common Tips and Advanced Techniques
You can learn more advanced techniques in the agent application building process through other documents in the Get_Start, such as how to add a memory module into the intelligent agent process and how to effectively manage prompts within the project.

Set up the visual agentic workflow platform
agentUniverse provides a visual canvas platform for creating agentic workflow. Follow these steps for a quick start:

Using pip

pip install magent-ui ruamel.yaml
One-click Run

Run product_application.py in ample_apps/workflow_agent_app/bootstrap/platform for quick startup.

For more details, refer to [Quick Start for Product Platform](docs/guidebook/en/How-to/Guide to Visual Agentic Workflow Platform/Product_Platform_Quick_Start.md) and the [Advanced Guide](docs/guidebook/en/How-to/Guide to Visual Agentic Workflow Platform/Product_Platform_Advancement_Guide.md).

This feature is jointly developed by difizen and agentUniverse.

Why use agentUniverse
Concept


The core of agentUniverse provides all the essential components needed to build a single intelligent agent, the collaboration mechanisms between multiple agents, and allows for the injection of expert knowledge. The enables developers to effortlessly create intelligent applications equipped with professional know-how.

Multi Agent Collaboration
agentUniverse offers several multi-agent collaboration model components that have been validated in real-world industries. Among these, the "PEER" model stands out as one of the most distinctive.

The PEER model utilizes agents with four distinct responsibilities: Planning, Executing, Expressing, and Reviewing. This structure allows for the decomposition and step-by-step execution of complex problems and enables autonomous iteration based on evaluation feedback, ultimately enhancing performance in reasoning and analytical tasks. This model is particularly effective in scenarios that require multi-step decomposition and in-depth analysis, such as event interpretation, macroeconomic analysis, and the feasibility analysis of business proposals.

The PEER model has achieved impressive results, and the latest research findings and experimental data can be found in the following literature.

Key Features
Based on the above introduction, we summarize the main features of agentUniverse as follow:

Flexible and Extensible Agent Construction Capability: It provides all the essential components necessary for building agents, all of which support customization to tailor user-specific agents.

Rich and Effective Multi-Agent Collaboration Models: It offers collaborative models such as PEER (Plan/Execute/Express/Review) and DOE (Data-finding/Opinion-inject/Express), which have been validated in the industry. Users can also customize and orchestrate new models to facilitate organic collaboration among multiple agents.

Easy Integration of Domain Expertise: It offers capabilities for domain prompts, knowledge construction, and management, enabling the orchestration and injection of domain-level SOPs, aligning agents with expert-level domain knowledge.

💡 For additional features: see the section on key features of agentUniverse for more details.

Sample Apps
🚩 Legal Advice Agent v2

🚩 Python Code Generation and Execution Agent

🚩 Discussion Group Based on Multi-Turn Multi-Agent Mode

🚩 Financial Event Analysis Based on PEER Multi-Agent Mode

🚩 Andrew Ng's Reflexive Workflow Translation Agent Replication

Commercial Product base on agentUniverse
🔗 Zhi Xiao Zhu-AI Assistant for Financial Professionals

Zhi Xiao Zhu AI Assistant: Designed to facilitate the development of large models in rigorous industries to enhance the productively of investment research experts

Zhi Xiao Zhu AI Assistant an efficient solution for the practical application of large models in rigorous industries. It is built upon the Finix model, which emphasizes precise applications, and leverages the agentUniverse intelligent agent framework, known for its professional customization capabilities. This solution targets a range of professional AI business assistants related to investment research, ESG (environmental, social, and governance), finance, earnings reports, and other specialized domains. It has been extensively validated in large-scale scenarios at Ant Group, significantly improving expert efficiency.

 zhixiaozhu_video.mp4 
Documents
User Guide
💡 For more detailed information, please refer to the User Guide.

API Reference
💡 Please consult the API Reference for technical details.

Support
Submit Questions via GitHub Issues
😊 We recommend submitting your queries using GitHub Issues, we typically respond within 2 business days.

Contact Us via Discord
😊 Join our Discord Channel to interact with us.

Contact Us via Administrator Email
😊 Email:

<EMAIL>
<EMAIL>
<EMAIL>
twitter
ID: @agentuniverse_

Acknowledgements
This project is partially built upon excellent open-source projects such as Langchain, Pydantic, Gunicorn, Flask, SQLAlchemy, chromadb, etc. (The detailed dependency list can be found in pyproject.toml). We would like to express our heartfelt gratitude to the related projects and their contributors. 🙏🙏🙏

About
agentUniverse is a LLM multi-agent framework that allows developers to easily build multi-agent applications.

Topics
python agent awesome ai multi-agent autonomous awesome-list ai-agents llm
Resources
 Readme
License
 Apache-2.0 license
 Activity
 Custom properties
Stars
 1.5k stars
Watchers
 16 watching
Forks
 213 forks
Report repository
Releases 15
v0.0.17
Latest
3 weeks ago
+ 14 releases
Packages
No packages published
Contributors
27
@LandJerry
@EdwinInAu
@weizjajj
@AniviaTn
@keithkiden
@DongXu-Zhang
@z4656207
@BroKun
@sunshinesmilelk
@goodgood-good
@ScholarAegean
@zhjuzi
@InphinitiZ
+ 13 contributors
Languages
Python
100.0%
Footer
© 2025 GitHub, Inc.
Footer navigation
Terms
Privacy
Security
Status
