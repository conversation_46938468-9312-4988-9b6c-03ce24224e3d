"""
Debug Agent Import Script

Tests importing agents using the actual package structure that AgentUniverse would use.
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_individual_agent_import():
    """Test importing each failing agent individually"""
    
    print("🔬 Debug Agent Import Test")
    print("=" * 50)
    
    # Test CodeExecutorAgent
    print("\n🔍 Testing CodeExecutorAgent...")
    try:
        from agents.core_analysis.executor_agent.code_executor_agent.code_executor_agent import CodeExecutorAgent
        agent = CodeExecutorAgent()
        print("   ✅ SUCCESS: CodeExecutorAgent imported and instantiated")
    except Exception as e:
        print(f"   ❌ FAILED: {e}")
        import traceback
        traceback.print_exc()
    
    # Test StorytellerAgent
    print("\n🔍 Testing StorytellerAgent...")
    try:
        from agents.specialized_business.storyteller_agent.storyteller_agent import StorytellerAgent
        agent = StorytellerAgent()
        print("   ✅ SUCCESS: StorytellerAgent imported and instantiated")
    except Exception as e:
        print(f"   ❌ FAILED: {e}")
        import traceback
        traceback.print_exc()
    
    # Test AgentUniverseOrchestrator
    print("\n🔍 Testing AgentUniverseOrchestrator...")
    try:
        from agents.orchestrator.agent_universe_orchestrator.agent_universe_orchestrator import AgentUniverseOrchestrator
        agent = AgentUniverseOrchestrator()
        print("   ✅ SUCCESS: AgentUniverseOrchestrator imported and instantiated")
    except Exception as e:
        print(f"   ❌ FAILED: {e}")
        import traceback
        traceback.print_exc()

def test_working_agent():
    """Test importing a working agent for comparison"""
    print("\n🔍 Testing a known working agent (PlannerAgent)...")
    try:
        from agents.core_analysis.planner_agent.planner_agent import PlannerAgent
        agent = PlannerAgent()
        print("   ✅ SUCCESS: PlannerAgent imported and instantiated")
    except Exception as e:
        print(f"   ❌ FAILED: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 Starting debug script...")
    test_working_agent()
    test_individual_agent_import()
    print("✅ Debug script completed.")
