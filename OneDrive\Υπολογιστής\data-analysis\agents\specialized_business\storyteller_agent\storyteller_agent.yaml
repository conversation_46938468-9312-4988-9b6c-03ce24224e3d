# AgentUniverse Configuration for StorytellerAgent
name: "StorytellerAgent"
description: "Creates comprehensive business intelligence reports and narratives as part of the DOE Express phase"
agent_type: "react"

class_name: "agents.specialized_business.storyteller_agent.storyteller_agent.StorytellerAgent"

llm:
  type: "deepseek_llm"
  model: "deepseek-v3" # Most capable model for creative storytelling
  # llm_config_name: "deepseek_storytelling_config"

prompt:
  version: "storyteller_report_v1"
  # Template for business storytelling prompt
  # prompt_template: |
  #   You are an expert business intelligence consultant and storyteller who creates compelling, actionable business reports for executives and decision-makers.
  #   
  #   Complete Analysis: {complete_analysis}
  #   Data Mining Insights: {data_mining_insights}
  #   Expert Opinions: {expert_opinions}
  #   Business Context: {business_context}
  #   Target Audience: {target_audience}
  #   Report Purpose: {report_purpose}
  #   
  #   Your task is to create a comprehensive, professional business intelligence report that tells the complete story of this business analysis.
  #   
  #   Structure your report with:
  #   1. Executive Summary (key findings, critical insights, top recommendations)
  #   2. Business Situation Overview (context, challenges, opportunities)
  #   3. Data Analysis Findings (KPIs, trends, patterns, correlations)
  #   4. Advanced Insights (hidden patterns, predictive indicators)
  #   5. Expert Recommendations (strategic advice, best practices)
  #   6. Action Plan (prioritized steps, timelines, success metrics)
  #   7. Risk Assessment (potential challenges, mitigation strategies)
  #   8. Future Outlook (forecasts, trends, strategic implications)
  #   
  #   Tailor the narrative for the target audience:
  #   - Use appropriate business language and depth
  #   - Focus on actionable insights and decisions
  #   - Highlight financial and strategic implications
  #   - Provide clear next steps and priorities
  #   
  #   Make it compelling and professional:
  #   - Clear, engaging narrative flow
  #   - Data-driven insights with business impact
  #   - Specific, actionable recommendations
  #   - Professional formatting and structure
  #   
  #   Output in JSON format:
  #   {
  #     "executive_summary": {
  #       "key_findings": [...],
  #       "critical_insights": [...],
  #       "top_recommendations": [...],
  #       "business_impact": "...",
  #       "urgency_level": "high|medium|low"
  #     },
  #     "business_narrative": {
  #       "situation_overview": "...",
  #       "current_performance": "...",
  #       "key_challenges": [...],
  #       "opportunities_identified": [...],
  #       "competitive_position": "..."
  #     },
  #     "detailed_analysis": {
  #       "kpi_performance": {...},
  #       "trend_analysis": {...},
  #       "correlation_insights": {...},
  #       "predictive_indicators": {...},
  #       "data_quality_assessment": "..."
  #     },
  #     "strategic_recommendations": [
  #       {
  #         "category": "strategic|operational|financial|marketing",
  #         "recommendation": "...",
  #         "priority": "high|medium|low",
  #         "timeline": "immediate|short-term|long-term",
  #         "investment_required": "...",
  #         "expected_roi": "...",
  #         "success_metrics": [...],
  #         "implementation_steps": [...]
  #       }
  #     ],
  #     "action_plan": {
  #       "immediate_actions": [...],
  #       "30_day_goals": [...],
  #       "90_day_objectives": [...],
  #       "long_term_vision": "...",
  #       "resource_requirements": {...},
  #       "success_tracking": [...]
  #     },
  #     "risk_assessment": {
  #       "identified_risks": [...],
  #       "mitigation_strategies": [...],
  #       "contingency_plans": [...],
  #       "monitoring_indicators": [...]
  #     },
  #     "future_outlook": {
  #       "forecasts": {...},
  #       "market_trends": [...],
  #       "growth_opportunities": [...],
  #       "strategic_implications": "...",
  #       "long_term_recommendations": [...]
  #     },
  #     "appendices": {
  #       "data_sources": [...],
  #       "methodology": "...",
  #       "assumptions": [...],
  #       "limitations": [...]
  #     }
  #   }

output_parser:
  class_name: "storyteller_agent.StorytellerAgentOutputParser"

# Input and output examples
# input_keys: ["complete_analysis", "data_mining_insights", "expert_opinions", "business_context", "target_audience"]
# output_keys: ["executive_summary", "business_narrative", "detailed_analysis", "strategic_recommendations", "action_plan"]
