# AgentUniverse Configuration for PlannerAgent
# File: c:\Users\<USER>\OneDrive\Υπολογιστής\data-analysis\agents\core_analysis\planner_agent\planner_agent.yaml

# Basic agent definition
name: "PlannerAgent"
description: "Receives user query and pre-analysis, then generates a detailed execution plan for data analysis."
agent_type: "react" # Or other relevant AgentUniverse agent types like "singleton"

# Python class implementing the agent's logic
class_name: "planner_agent.PlannerAgent" # Points to PlannerAgent class in planner_agent.py
# If your planner_agent.py is in a subdirectory of the agent's config folder, adjust path.
# e.g., if planner_agent.py is in agents/core_analysis/planner_agent/src/planner_agent.py
# then class_name: "src.planner_agent.PlannerAgent"

# LLM configuration
# This will use the DeepSeek model configured in the main AgentUniverse config or custom_key.toml
llm:
  type: "deepseek_llm" # Assuming you have a deepseek_llm type defined
  # Parameters for the LLM call, if needed (e.g., temperature, max_tokens)
  # These might be overridden by the prompt's configuration
  # llm_config_name: "deepseek_chat_config" # If you define named LLM configs

# Prompt configuration
# Prompts are typically managed separately in a prompts directory or defined here.
# For now, we'll assume a prompt will be developed.
prompt:
  version: "0.0.1" # Version of the prompt to use
  # prompt_template: |
  #   You are an AI Data Analysis Planner.
  #   User Query: {user_query}
  #   Data Pre-analysis (Business Type: {business_type}, Suggested KPIs: {suggested_kpis}, Data Structure: {data_structure_assessment}):
  #   {pre_analysis_output}
  #
  #   Based on the above, generate a step-by-step plan to address the user query.
  #   The plan should be a list of tasks for executor agents.
  #   Each task should specify:
  #   - task_id
  #   - agent_to_use (e.g., DataAnalyzerAgent, KPIDiscoveryAgent, CodeExecutorAgent)
  #   - inputs_for_agent
  #   - expected_output_description
  #
  #   Output the plan in JSON format.
  #
  #   Plan:

# Output parser (optional, if custom parsing is needed beyond simple LLM string output)
output_parser:
  class_name: "planner_agent.PlannerAgentOutputParser"

# Memory (optional, if the agent needs to maintain state or use memory components)
# memory:
#   type: "your_memory_type"
#   config:
#     # memory specific configurations

# Tools (optional, if the agent needs to use specific tools)
# tools:
#   - name: "data_loader_tool"
#     class_name: "path.to.your.DataLoaderTool"

# Input and Output examples (good for documentation and testing)
# input_keys: ["user_query", "pre_analysis_output"]
# output_keys: ["plan"]
