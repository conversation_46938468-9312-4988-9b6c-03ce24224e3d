"""
Test script to verify real storyteller agent execution
"""

import asyncio
import json
import logging
from core.enhanced_orchestrator_temp import EnhancedAgentUniverseOrchestrator

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_real_storyteller():
    """Test the real storyteller agent execution"""
    
    # Sample test data
    test_data = {
        "filename": "test_business_data.csv",
        "data": {
            "sales": [1000, 1200, 1100, 1300, 1250],
            "region": ["North", "South", "East", "West", "Central"],
            "month": ["Jan", "Feb", "Mar", "Apr", "May"],
            "profit": [200, 240, 220, 260, 250]
        }
    }
    
    # Initialize orchestrator
    orchestrator = EnhancedAgentUniverseOrchestrator()
    
    try:
        logger.info("🚀 Testing real storyteller agent execution...")
          # Process the data
        result = await orchestrator.execute(input_object=test_data, agent_input={"selected_kpis": ["sales", "profit"]})
        
        # Check if storyteller was used
        storyteller_result = result.get("agent_results", {}).get("storyteller", {})
        
        if storyteller_result.get("status") == "completed":
            logger.info("✅ Real StorytellerAgent executed successfully!")
            logger.info(f"Executive Summary: {storyteller_result.get('executive_summary', {})}")
            logger.info(f"Action Plan: {storyteller_result.get('action_plan', {})}")
        else:
            logger.warning("⚠️ StorytellerAgent may have used fallback")
            
        # Check the storytelling narrative
        narrative = result.get("storytelling_narrative", {})
        if narrative:
            logger.info("📖 Storytelling narrative generated:")
            logger.info(f"Narrative metadata: {narrative.get('storytelling_metadata', {})}")
            
        return result
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return None

if __name__ == "__main__":
    result = asyncio.run(test_real_storyteller())
    
    if result:
        print("\n🎉 Test completed successfully!")
        
        # Show key results
        storyteller_data = result.get("agent_results", {}).get("storyteller", {})
        print(f"\nStoryteller Status: {storyteller_data.get('status', 'unknown')}")
        print(f"Agent Type: {storyteller_data.get('agent_type', 'unknown')}")
        
        if "executive_summary" in storyteller_data:
            print(f"\nExecutive Summary Available: ✅")
        if "narrative_report" in storyteller_data:
            print(f"Narrative Report Available: ✅")
        if "action_plan" in storyteller_data:
            print(f"Action Plan Available: ✅")
    else:
        print("\n❌ Test failed!")
