"""
File Upload Handler for AI Data Analysis Platform

Handles various file formats (Excel, CSV, JSON) and prepares data
for DeepSeek V3 analysis and agent swarm processing.
"""

import pandas as pd
import json
import logging
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
import chardet
from datetime import datetime
import mimetypes
import os

logger = logging.getLogger(__name__)


class FileUploadHandler:
    """
    Handles file uploads and data preparation for the AI analysis pipeline
    
    Supports:
    - CSV files
    - Excel files (.xlsx, .xls)
    - JSON files
    - Future: SQL database connections
    """
    
    def __init__(self):
        self.supported_formats = {
            'csv': self._process_csv,
            'excel': self._process_excel,
            'json': self._process_json
        }
          def process_uploaded_file(self, file_path: Optional[str] = None, file_content: Optional[bytes] = None) -> Dict:
        """
        Main method to process uploaded files
        
        Args:
            file_path: Path to the uploaded file
            file_content: Raw file content (bytes)
            
        Returns:
            Processed data ready for DeepSeek analysis
        """        try:
            # Validate inputs
            if not file_path and not file_content:
                return {
                    "error": "Either file_path or file_content must be provided",
                    "processing_status": "failed"
                }
            
            # Use default filename if only content is provided
            if not file_path:
                file_path = "uploaded_file.csv"  # Default assumption
            
            # Determine file type
            file_info = self._analyze_file(file_path, file_content or b"")
            
            # Process based on file type
            file_format = file_info['format']
            if file_format in self.supported_formats:
                processed_data = self.supported_formats[file_format](file_path, file_content)
            else:
                raise ValueError(f"Unsupported file format: {file_format}")
            
            # Enhance with metadata
            result = {
                "file_metadata": file_info,
                "uploaded_file_data": processed_data,
                "processing_timestamp": datetime.now().isoformat(),
                "processing_status": "success"
            }
            
            logger.info(f"✅ File processed successfully: {file_info['filename']}")
            return result
            
        except Exception as e:
            logger.error(f"❌ File processing failed: {e}")
            return {
                "error": str(e),
                "processing_status": "failed",
                "file_metadata": self._get_basic_file_info(file_path)
            }
    
    def _analyze_file(self, file_path: str, file_content: bytes = None) -> Dict:
        """Analyze file and determine its characteristics"""
        path_obj = Path(file_path)
        
        # Basic file info
        file_info = {
            "filename": path_obj.name,
            "file_extension": path_obj.suffix.lower(),
            "file_size": len(file_content) if file_content else path_obj.stat().st_size if path_obj.exists() else 0
        }
        
        # Determine format
        extension = file_info["file_extension"]
        if extension == '.csv':
            file_info["format"] = "csv"
            file_info["mime_type"] = "text/csv"
        elif extension in ['.xlsx', '.xls']:
            file_info["format"] = "excel"
            file_info["mime_type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        elif extension == '.json':
            file_info["format"] = "json"
            file_info["mime_type"] = "application/json"
        else:
            file_info["format"] = "unknown"
            file_info["mime_type"] = "unknown"
        
        # Detect encoding for text files
        if file_content and file_info["format"] in ["csv", "json"]:
            try:
                encoding_result = chardet.detect(file_content)
                file_info["encoding"] = encoding_result.get("encoding", "utf-8")
                file_info["encoding_confidence"] = encoding_result.get("confidence", 0)
            except:
                file_info["encoding"] = "utf-8"
                file_info["encoding_confidence"] = 0
        
        return file_info
    
    def _process_csv(self, file_path: str, file_content: bytes = None) -> Dict:
        """Process CSV files"""
        try:
            if file_content:
                # Detect encoding
                encoding = chardet.detect(file_content).get("encoding", "utf-8")
                # Read from bytes
                import io
                df = pd.read_csv(io.BytesIO(file_content), encoding=encoding)
            else:
                df = pd.read_csv(file_path)
            
            # Convert to structured format
            processed_data = {
                "data": df.to_dict('records'),
                "columns": list(df.columns),
                "row_count": len(df),
                "column_count": len(df.columns),
                "data_types": self._analyze_column_types(df),
                "sample_data": df.head(5).to_dict('records'),
                "data_quality": self._assess_data_quality(df)
            }
            
            return processed_data
            
        except Exception as e:
            logger.error(f"CSV processing error: {e}")
            raise
    
    def _process_excel(self, file_path: str, file_content: bytes = None) -> Dict:
        """Process Excel files"""
        try:
            if file_content:
                import io
                excel_data = pd.read_excel(io.BytesIO(file_content), sheet_name=None)
            else:
                excel_data = pd.read_excel(file_path, sheet_name=None)
            
            # Handle multiple sheets
            if len(excel_data) == 1:
                # Single sheet
                sheet_name = list(excel_data.keys())[0]
                df = excel_data[sheet_name]
                
                processed_data = {
                    "data": df.to_dict('records'),
                    "columns": list(df.columns),
                    "row_count": len(df),
                    "column_count": len(df.columns),
                    "data_types": self._analyze_column_types(df),
                    "sample_data": df.head(5).to_dict('records'),
                    "data_quality": self._assess_data_quality(df),
                    "sheet_info": {
                        "active_sheet": sheet_name,
                        "total_sheets": 1
                    }
                }
            else:
                # Multiple sheets - use the first one with data, provide info about others
                main_sheet = None
                sheet_info = {}
                
                for sheet_name, df in excel_data.items():
                    sheet_info[sheet_name] = {
                        "row_count": len(df),
                        "column_count": len(df.columns),
                        "has_data": not df.empty
                    }
                    
                    if main_sheet is None and not df.empty:
                        main_sheet = df
                        active_sheet_name = sheet_name
                
                if main_sheet is not None:
                    processed_data = {
                        "data": main_sheet.to_dict('records'),
                        "columns": list(main_sheet.columns),
                        "row_count": len(main_sheet),
                        "column_count": len(main_sheet.columns),
                        "data_types": self._analyze_column_types(main_sheet),
                        "sample_data": main_sheet.head(5).to_dict('records'),
                        "data_quality": self._assess_data_quality(main_sheet),
                        "sheet_info": {
                            "active_sheet": active_sheet_name,
                            "total_sheets": len(excel_data),
                            "all_sheets": sheet_info
                        }
                    }
                else:
                    raise ValueError("No data found in any Excel sheet")
            
            return processed_data
            
        except Exception as e:
            logger.error(f"Excel processing error: {e}")
            raise
    
    def _process_json(self, file_path: str, file_content: bytes = None) -> Dict:
        """Process JSON files"""
        try:
            if file_content:
                encoding = chardet.detect(file_content).get("encoding", "utf-8")
                json_str = file_content.decode(encoding)
                data = json.loads(json_str)
            else:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            
            # Handle different JSON structures
            if isinstance(data, list):
                # Array of objects
                if data and isinstance(data[0], dict):
                    df = pd.DataFrame(data)
                    processed_data = {
                        "data": data,
                        "columns": list(df.columns),
                        "row_count": len(data),
                        "column_count": len(df.columns),
                        "data_types": self._analyze_column_types(df),
                        "sample_data": data[:5],
                        "data_quality": self._assess_data_quality(df),
                        "json_structure": "array_of_objects"
                    }
                else:
                    # Simple array
                    processed_data = {
                        "data": [{"value": item, "index": i} for i, item in enumerate(data)],
                        "columns": ["index", "value"],
                        "row_count": len(data),
                        "column_count": 2,
                        "json_structure": "simple_array"
                    }
            elif isinstance(data, dict):
                # Object structure
                flattened = self._flatten_json(data)
                processed_data = {
                    "data": [flattened],
                    "columns": list(flattened.keys()),
                    "row_count": 1,
                    "column_count": len(flattened),
                    "json_structure": "single_object",
                    "original_structure": data
                }
            else:
                # Single value
                processed_data = {
                    "data": [{"value": data}],
                    "columns": ["value"],
                    "row_count": 1,
                    "column_count": 1,
                    "json_structure": "single_value"
                }
            
            return processed_data
            
        except Exception as e:
            logger.error(f"JSON processing error: {e}")
            raise
    
    def _analyze_column_types(self, df: pd.DataFrame) -> Dict:
        """Analyze column data types"""
        column_types = {}
        for column in df.columns:
            try:
                # Get pandas dtype
                pandas_type = str(df[column].dtype)
                
                # Infer business meaning
                if 'int' in pandas_type or 'float' in pandas_type:
                    business_type = "numeric"
                elif 'datetime' in pandas_type:
                    business_type = "datetime"
                elif 'object' in pandas_type:
                    # Further analysis for object types
                    sample_values = df[column].dropna().head(10).astype(str)
                    if any(keyword in ' '.join(sample_values).lower() 
                           for keyword in ['date', '2023', '2024', '2025', '/']):
                        business_type = "potential_date"
                    elif df[column].nunique() < len(df) * 0.8:
                        business_type = "categorical"
                    else:
                        business_type = "text"
                else:
                    business_type = "unknown"
                
                column_types[column] = {
                    "pandas_type": pandas_type,
                    "business_type": business_type,
                    "null_count": df[column].isnull().sum(),
                    "unique_count": df[column].nunique(),
                    "sample_values": df[column].dropna().head(3).tolist()
                }
            except:
                column_types[column] = {"error": "type_analysis_failed"}
        
        return column_types
    
    def _assess_data_quality(self, df: pd.DataFrame) -> Dict:
        """Assess data quality metrics"""
        total_cells = df.size
        null_cells = df.isnull().sum().sum()
        
        return {
            "completeness": round((total_cells - null_cells) / total_cells * 100, 2),
            "total_rows": len(df),
            "total_columns": len(df.columns),
            "null_percentage": round(null_cells / total_cells * 100, 2),
            "duplicate_rows": df.duplicated().sum(),
            "quality_score": self._calculate_quality_score(df)
        }
    
    def _calculate_quality_score(self, df: pd.DataFrame) -> str:
        """Calculate overall data quality score"""
        null_percentage = df.isnull().sum().sum() / df.size * 100
        duplicate_percentage = df.duplicated().sum() / len(df) * 100
        
        if null_percentage < 5 and duplicate_percentage < 2:
            return "high"
        elif null_percentage < 15 and duplicate_percentage < 10:
            return "medium"
        else:
            return "low"
    
    def _flatten_json(self, data: Dict, parent_key: str = '', sep: str = '_') -> Dict:
        """Flatten nested JSON structure"""
        items = []
        for k, v in data.items():
            new_key = f"{parent_key}{sep}{k}" if parent_key else k
            if isinstance(v, dict):
                items.extend(self._flatten_json(v, new_key, sep=sep).items())
            elif isinstance(v, list):
                if v and isinstance(v[0], dict):
                    # Take first item as representative
                    items.extend(self._flatten_json(v[0], new_key, sep=sep).items())
                else:
                    items.append((new_key, str(v)))
            else:
                items.append((new_key, v))
        return dict(items)
    
    def _get_basic_file_info(self, file_path: str) -> Dict:
        """Get basic file information for error cases"""
        try:
            path_obj = Path(file_path)
            return {
                "filename": path_obj.name,
                "file_extension": path_obj.suffix,
                "error": "processing_failed"
            }
        except:
            return {"error": "file_info_unavailable"}


class DatabaseConnector:
    """
    Future implementation for SQL database connections
    As mentioned in your vision: "i hope latter to connect it with sqls"
    """
    
    def __init__(self):
        self.supported_databases = ['mysql', 'postgresql', 'sqlite', 'sqlserver']
    
    def connect_to_database(self, connection_string: str, query: str) -> Dict:
        """
        Future implementation for database connections
        """
        # TODO: Implement database connectivity
        return {
            "status": "not_implemented",
            "message": "Database connectivity will be implemented in the next phase"
        }


# Example usage and testing
def test_file_handler():
    """Test file handler with sample data"""
    handler = FileUploadHandler()
    
    # Create sample CSV content for testing
    sample_csv = """date,revenue,customers,orders
2024-01-01,15000,450,120
2024-01-02,17500,520,135
2024-01-03,12000,380,95"""
    
    # Test with bytes content
    csv_bytes = sample_csv.encode('utf-8')
    result = handler.process_uploaded_file("test_sales.csv", csv_bytes)
    
    print("File Processing Result:")
    print(json.dumps(result, indent=2, default=str))


if __name__ == "__main__":
    test_file_handler()
