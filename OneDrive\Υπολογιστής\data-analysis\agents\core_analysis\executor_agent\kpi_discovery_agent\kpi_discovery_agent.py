"""
KPIDiscoveryAgent: Identifies and calculates Key Performance Indicators from business data as part of the PEER Execute phase.
"""
from agentuniverse.agent.agent import Agent
from typing import Any, Dict, List
import pandas as pd
import numpy as np
import json

class KPIDiscoveryAgent(Agent):
    """Discovers and calculates business KPIs from data structure and context."""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def input_keys(self) -> list[str]:
        return ["dataset", "business_type", "suggested_kpis", "business_context"]

    def output_keys(self) -> list[str]:
        return ["discovered_kpis", "kpi_calculations", "business_metrics", "recommendations"]

    def parse_input(self, input_object, agent_input):
        return {
            "dataset": agent_input.get("dataset"),
            "business_type": agent_input.get("business_type", ""),
            "suggested_kpis": agent_input.get("suggested_kpis", []),
            "business_context": agent_input.get("business_context", {})
        }

    def parse_result(self, agent_result):
        return agent_result

    def execute(self, input_object, agent_input):
        """
        Discover and calculate KPIs based on business context and data structure.
        """
        dataset = agent_input.get("dataset")
        business_type = agent_input.get("business_type", "")
        suggested_kpis = agent_input.get("suggested_kpis", [])
        business_context = agent_input.get("business_context", {})

        if dataset is None:
            return {"error": "No dataset provided", "success": False}

        try:
            # Convert to DataFrame if needed
            if isinstance(dataset, dict):
                df = pd.DataFrame(dataset)
            elif isinstance(dataset, list):
                df = pd.DataFrame(dataset)
            elif isinstance(dataset, pd.DataFrame):
                df = dataset
            else:
                return {"error": "Unsupported dataset format", "success": False}

            # Discover KPIs based on data structure and business type
            discovered_kpis = self._discover_kpis_from_data(df, business_type)
            
            # Calculate suggested KPIs if possible
            calculated_kpis = self._calculate_suggested_kpis(df, suggested_kpis)
            
            # Generate business metrics
            business_metrics = self._generate_business_metrics(df, business_type)
            
            # Combine all KPIs
            all_kpis = {**discovered_kpis, **calculated_kpis}
            
            # Generate recommendations
            recommendations = self._generate_kpi_recommendations(df, all_kpis, business_type)

            return {
                "discovered_kpis": all_kpis,
                "kpi_calculations": calculated_kpis,
                "business_metrics": business_metrics,
                "recommendations": recommendations,
                "success": True
            }

        except Exception as e:
            return {
                "error": f"KPI discovery failed: {str(e)}",
                "success": False
            }

    def _discover_kpis_from_data(self, df: pd.DataFrame, business_type: str) -> Dict[str, Any]:
        """Discover potential KPIs based on data structure"""
        kpis = {}
        columns = df.columns.tolist()
        
        # Revenue-related KPIs
        revenue_cols = [col for col in columns if any(term in col.lower() 
                       for term in ['revenue', 'sales', 'income', 'turnover', 'earnings'])]
        for col in revenue_cols:
            if pd.api.types.is_numeric_dtype(df[col]):
                kpis[f"total_{col.lower()}"] = {
                    "value": float(df[col].sum()),
                    "type": "revenue",
                    "description": f"Total {col} across all records"
                }
                kpis[f"average_{col.lower()}"] = {
                    "value": float(df[col].mean()),
                    "type": "revenue",
                    "description": f"Average {col} per record"
                }
        
        # Customer-related KPIs
        customer_cols = [col for col in columns if any(term in col.lower() 
                        for term in ['customer', 'client', 'user', 'buyer'])]
        for col in customer_cols:
            if col in df.columns:
                kpis[f"unique_{col.lower()}_count"] = {
                    "value": int(df[col].nunique()),
                    "type": "customer",
                    "description": f"Number of unique {col}"
                }
        
        # Volume/Quantity KPIs
        quantity_cols = [col for col in columns if any(term in col.lower() 
                        for term in ['quantity', 'volume', 'count', 'number', 'amount'])]
        for col in quantity_cols:
            if pd.api.types.is_numeric_dtype(df[col]):
                kpis[f"total_{col.lower()}"] = {
                    "value": float(df[col].sum()),
                    "type": "volume",
                    "description": f"Total {col}"
                }
        
        return kpis

    def _calculate_suggested_kpis(self, df: pd.DataFrame, suggested_kpis: List[str]) -> Dict[str, Any]:
        """Calculate specific suggested KPIs if data allows"""
        calculated = {}
        
        for kpi in suggested_kpis:
            kpi_lower = kpi.lower()
            
            # Revenue-based calculations
            if 'revenue' in kpi_lower or 'sales' in kpi_lower:
                revenue_cols = [col for col in df.columns if any(term in col.lower() 
                               for term in ['revenue', 'sales', 'income'])]
                if revenue_cols:
                    total_revenue = df[revenue_cols[0]].sum() if pd.api.types.is_numeric_dtype(df[revenue_cols[0]]) else 0
                    calculated[kpi] = {
                        "value": float(total_revenue),
                        "type": "calculated",
                        "source_column": revenue_cols[0],
                        "description": f"Calculated {kpi} from {revenue_cols[0]}"
                    }
            
            # Growth rate calculations
            elif 'growth' in kpi_lower:
                numeric_cols = df.select_dtypes(include=[np.number]).columns
                if len(numeric_cols) > 0 and len(df) > 1:
                    col = numeric_cols[0]
                    if len(df) >= 2:
                        first_value = df[col].iloc[0]
                        last_value = df[col].iloc[-1]
                        if first_value != 0:
                            growth_rate = ((last_value - first_value) / first_value) * 100
                            calculated[kpi] = {
                                "value": float(growth_rate),
                                "type": "calculated",
                                "source_column": col,
                                "description": f"Growth rate for {col}: {growth_rate:.2f}%"
                            }
        
        return calculated

    def _generate_business_metrics(self, df: pd.DataFrame, business_type: str) -> Dict[str, Any]:
        """Generate business-specific metrics based on business type"""
        metrics = {}
        
        if business_type.lower() in ['retail', 'ecommerce', 'sales']:
            metrics.update(self._retail_metrics(df))
        elif business_type.lower() in ['finance', 'financial', 'banking']:
            metrics.update(self._financial_metrics(df))
        elif business_type.lower() in ['marketing', 'advertising']:
            metrics.update(self._marketing_metrics(df))
        else:
            metrics.update(self._general_business_metrics(df))
        
        return metrics

    def _retail_metrics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate retail-specific metrics"""
        metrics = {}
        
        # Average order value
        amount_cols = [col for col in df.columns if any(term in col.lower() 
                      for term in ['amount', 'total', 'value', 'price'])]
        if amount_cols:
            metrics["average_order_value"] = {
                "value": float(df[amount_cols[0]].mean()),
                "description": "Average order value"
            }
        
        # Transaction count
        metrics["total_transactions"] = {
            "value": len(df),
            "description": "Total number of transactions"
        }
        
        return metrics

    def _financial_metrics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate finance-specific metrics"""
        metrics = {}
        
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) >= 2:
            metrics["financial_ratio"] = {
                "value": float(df[numeric_cols[0]].sum() / df[numeric_cols[1]].sum()),
                "description": f"Ratio of {numeric_cols[0]} to {numeric_cols[1]}"
            }
        
        return metrics

    def _marketing_metrics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate marketing-specific metrics"""
        metrics = {}
        
        conversion_cols = [col for col in df.columns if 'conversion' in col.lower()]
        if conversion_cols:
            metrics["conversion_rate"] = {
                "value": float(df[conversion_cols[0]].mean() * 100),
                "description": "Average conversion rate (%)"
            }
        
        return metrics

    def _general_business_metrics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate general business metrics"""
        metrics = {}
        
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        
        if len(numeric_cols) > 0:
            performance_sum = df[numeric_cols].sum().sum()
            metrics["performance_index"] = {
                "value": float(performance_sum),
                "description": "Overall performance index (sum of all numeric values)"
            }
        
        return metrics

    def _generate_kpi_recommendations(self, df: pd.DataFrame, kpis: Dict[str, Any], business_type: str) -> List[str]:
        """Generate recommendations for KPI improvement"""
        recommendations = []
        
        # Data completeness recommendations
        missing_data = df.isnull().sum().sum()
        if missing_data > 0:
            recommendations.append(f"Consider improving data quality - {missing_data} missing values detected")
        
        # KPI availability recommendations
        if len(kpis) < 5:
            recommendations.append("Consider adding more KPI-relevant columns to improve analysis depth")
        
        # Business-specific recommendations
        if business_type:
            recommendations.append(f"Focus on {business_type}-specific metrics for better business insights")
        
        return recommendations
