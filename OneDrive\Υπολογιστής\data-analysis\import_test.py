#!/usr/bin/env python3
"""
Import error test
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

print("🔍 Testing imports line by line...")

# Test the imports that these files use
print("\n1️⃣ Testing agentuniverse import...")
try:
    from agentuniverse.agent.agent import Agent
    print("✅ agentuniverse.agent.agent import successful")
except Exception as e:
    print(f"❌ agentuniverse import failed: {e}")
    print("🔧 This is likely the root cause!")

print("\n2️⃣ Testing other imports...")
try:
    import json, traceback, logging, io, sys
    from typing import Any, Dict, Optional
    from contextlib import redirect_stdout, redirect_stderr
    print("✅ Standard library imports successful")
except Exception as e:
    print(f"❌ Standard library import failed: {e}")

print("✅ Import test completed")
