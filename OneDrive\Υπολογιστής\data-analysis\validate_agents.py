
"""
Agent Validation Script

Validates that all agents are properly implemented and can be imported.
This script checks the complete agent architecture for the AI Data Analysis Platform.
"""

import sys
import os
import importlib.util
from pathlib import Path
import traceback

def test_agent_import(agent_path: str, agent_class_name: str) -> dict:
    """Test importing a single agent"""
    try:
        # Load the module
        spec = importlib.util.spec_from_file_location("agent_module", agent_path)
        if spec is None or spec.loader is None:
            return {"success": False, "error": "Could not load module spec"}
        
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        # Check if the agent class exists
        if not hasattr(module, agent_class_name):
            return {"success": False, "error": f"Class {agent_class_name} not found in module"}
        
        agent_class = getattr(module, agent_class_name)
        
        # Check if it has required methods
        required_methods = ["input_keys", "output_keys", "parse_input", "parse_result", "execute"]
        missing_methods = []
        
        for method in required_methods:
            if not hasattr(agent_class, method):
                missing_methods.append(method)
        
        if missing_methods:
            return {"success": False, "error": f"Missing methods: {missing_methods}"}
        
        # Try to instantiate (basic check)
        try:
            # Some agents might need special initialization, so we catch any errors
            instance = agent_class()
            return {"success": True, "message": "Agent imported and instantiated successfully"}
        except Exception as e:
            return {"success": True, "message": f"Agent imported successfully (instantiation failed: {str(e)})"}
            
    except Exception as e:
        return {"success": False, "error": f"Import failed: {str(e)}", "traceback": traceback.format_exc()}

def main():
    """Main validation function"""
    print("🚀 AI Data Analysis Platform - Agent Validation")
    print("=" * 60)
    
    # Define all agents to validate
    agents_to_validate = [
        # PEER Pattern Agents
        {
            "name": "PlannerAgent",
            "path": "agents/core_analysis/planner_agent/planner_agent.py",
            "class": "PlannerAgent",
            "pattern": "PEER - Plan"
        },
        {
            "name": "DataAnalyzerAgent", 
            "path": "agents/core_analysis/executor_agent/data_analyzer_agent/data_analyzer_agent.py",
            "class": "DataAnalyzerAgent",
            "pattern": "PEER - Execute"
        },
        {
            "name": "KPIDiscoveryAgent",
            "path": "agents/core_analysis/executor_agent/kpi_discovery_agent/kpi_discovery_agent.py", 
            "class": "KPIDiscoveryAgent",
            "pattern": "PEER - Execute"
        },
        {
            "name": "ProphetAgent",
            "path": "agents/core_analysis/executor_agent/prophet_agent/prophet_agent.py",
            "class": "ProphetAgent", 
            "pattern": "PEER - Execute"
        },
        {
            "name": "CodeExecutorAgent",
            "path": "agents/core_analysis/executor_agent/code_executor_agent/code_executor_agent.py",
            "class": "CodeExecutorAgent",
            "pattern": "PEER - Execute"
        },
        {
            "name": "ExpresserAgent",
            "path": "agents/core_analysis/expresser_agent/expresser_agent.py",
            "class": "ExpresserAgent", 
            "pattern": "PEER - Express"
        },
        {
            "name": "ReviewerAgent",
            "path": "agents/core_analysis/reviewer_agent/reviewer_agent.py",
            "class": "ReviewerAgent",
            "pattern": "PEER - Review"
        },
        
        # DOE Pattern Agents
        {
            "name": "DataFiningAgent",
            "path": "agents/specialized_business/data_fining_agent/data_fining_agent.py",
            "class": "DataFiningAgent",
            "pattern": "DOE - Data-fining"
        },
        {
            "name": "OpinionInjectAgent", 
            "path": "agents/specialized_business/opinion_inject_agent/opinion_inject_agent.py",
            "class": "OpinionInjectAgent",
            "pattern": "DOE - Opinion-inject"
        },
        {
            "name": "StorytellerAgent",
            "path": "agents/specialized_business/storyteller_agent/storyteller_agent.py", 
            "class": "StorytellerAgent",
            "pattern": "DOE - Express"
        },
        
        # Orchestrator
        {
            "name": "AgentUniverseOrchestrator",
            "path": "agents/orchestrator/agent_universe_orchestrator/agent_universe_orchestrator.py",
            "class": "AgentUniverseOrchestrator", 
            "pattern": "Master Controller"
        }
    ]
    
    # Get base directory
    base_dir = Path(__file__).parent
    
    # Track results
    total_agents = len(agents_to_validate)
    successful_agents = 0
    failed_agents = 0
    
    print(f"Validating {total_agents} agents...\n")
    
    # Validate each agent
    for agent_info in agents_to_validate:
        agent_path = base_dir / agent_info["path"]
        
        print(f"🔍 Testing {agent_info['name']} ({agent_info['pattern']})")
        print(f"   Path: {agent_path}")
        
        if not agent_path.exists():
            print(f"   ❌ FAILED: File does not exist")
            failed_agents += 1
            print()
            continue
        
        # Test import
        result = test_agent_import(str(agent_path), agent_info["class"])
        
        if result["success"]:
            print(f"   ✅ SUCCESS: {result['message']}")
            successful_agents += 1
        else:
            print(f"   ❌ FAILED: {result['error']}")
            if "traceback" in result:
                print(f"   📋 Traceback: {result['traceback'][:200]}...")
            failed_agents += 1
        
        print()
    
    # Summary
    print("=" * 60)
    print("🎯 VALIDATION SUMMARY")
    print("=" * 60)
    print(f"Total Agents: {total_agents}")
    print(f"✅ Successful: {successful_agents}")
    print(f"❌ Failed: {failed_agents}")
    print(f"Success Rate: {(successful_agents/total_agents)*100:.1f}%")
    
    if successful_agents == total_agents:
        print("\n🎉 ALL AGENTS VALIDATED SUCCESSFULLY!")
        print("✨ Your AI Data Analysis Platform agent swarm is ready!")
        
        print("\n🔗 Agent Architecture:")
        print("├── PEER Pattern (Plan/Execute/Express/Review)")
        print("│   ├── PlannerAgent (Strategic Planning)")
        print("│   ├── ExecutorAgents (Data Analysis, KPI Discovery, Forecasting, Code)")
        print("│   ├── ExpresserAgent (Initial Results)")
        print("│   └── ReviewerAgent (Quality Assurance)")
        print("│")
        print("├── DOE Pattern (Data-fining/Opinion-inject/Express)")
        print("│   ├── DataFiningAgent (Data Refinement)")
        print("│   ├── OpinionInjectAgent (Business Expertise)")
        print("│   └── StorytellerAgent (Business Intelligence Reports)")
        print("│")
        print("└── AgentUniverseOrchestrator (Master Controller)")
        
        print("\n🚀 Next Steps:")
        print("1. Create prompt templates for each agent")
        print("2. Configure DeepSeek API integration")
        print("3. Integrate with Node.js backend")
        print("4. Test with real data")
        print("5. Deploy to production")
        
    else:
        print(f"\n⚠️  {failed_agents} agents need attention before deployment")
        print("Please review and fix the failed agents above.")
    
    return successful_agents == total_agents

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
