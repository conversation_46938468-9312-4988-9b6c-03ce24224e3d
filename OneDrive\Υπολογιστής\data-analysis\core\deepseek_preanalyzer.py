"""
DeepSeek V3 Integration Module for AI Data Analysis Platform

This module handles all DeepSeek API interactions and provides intelligent
business analysis capabilities as the first layer before agent swarm activation.
"""

import httpx
import json
import asyncio
import logging
from typing import Dict, List, Optional, Any
from pathlib import Path
import toml

logger = logging.getLogger(__name__)


class DeepSeekPreAnalyzer:
    """
    DeepSeek V3 Pre-Analyzer - The Smart AI Brain
    -You are the master brain of the application, you should inspect the files that you get and combine the columsn,rows and provide deep insights to the user.
    -You MUST give specific and accurate responses cause you are the brain of a agents swearm.
    -in some cases you will meet the following:
     παραλήπτης=patient/ασθενής
     εντολέας=doctor/γιατρός
    This is the first layer that analyzes uploaded files to determine:
    - Business type and industry
    - Relevant KPIs for the specific business
    - Data structure and quality assessment   
    - Analysis strategy recommendations
    """
    
    def __init__(self, config_path: str = "config/deepseek_config.toml"):
        """Initialize DeepSeek Pre-Analyzer with configuration"""
        self.config = self._load_config(config_path)
        self.api_key = self.config["deepseek"]["api_key"]
        self.base_url = self.config["deepseek"]["base_url"]
        
        # Enhanced timeout configuration - INCREASED for complex analysis
        timeout_config = httpx.Timeout(
            connect=self.config["deepseek"]["settings"].get("connect_timeout", 30),
            read=self.config["deepseek"]["settings"].get("read_timeout", 300),  # ✅ INCREASED: 5 minutes
            write=60.0,  # ✅ INCREASED
            pool=120.0   # ✅ INCREASED
        )
        
        self.client = httpx.AsyncClient(
            base_url=self.base_url,
            headers={
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            },
            timeout=timeout_config
        )
        
    def _load_config(self, config_path: str) -> Dict:
        """Load DeepSeek configuration from TOML file"""
        try:
            return toml.load(config_path)
        except FileNotFoundError:
            logger.warning(f"Config file {config_path} not found. Using defaults.")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict:
        """Default configuration if config file is missing"""
        return {
            "deepseek": {
                "api_key": "***********************************",
                "base_url": "https://api.deepseek.com",
                "models": {
                    "primary": "deepseek-v3",
                    "reasoning": "deepseek-r1",
                    "efficient": "deepseek-r1-distill-qwen-32b"
                },
                "settings": {
                    "max_tokens": 4096,
                    "temperature": 0.1,
                    "top_p": 0.9,
                    "timeout": 60
                }
            }
        }
    
    async def analyze_business_data(self, file_data: Dict, file_metadata: Dict) -> Dict:
        """
        Primary analysis method - DeepSeek V3 analyzes the uploaded file
        and determines business context, KPIs, and analysis strategy.
        
        Args:
            file_data: Parsed data from uploaded file (CSV, Excel, JSON)
            file_metadata: File information (name, size, type, etc.)
            
        Returns:
            Comprehensive pre-analysis with business intelligence
        """
        logger.info("🧠 DeepSeek V3 Pre-Analysis starting...")
        
        try:
            # Prepare data sample for analysis
            data_sample = self._prepare_data_sample(file_data)
            
            # Business Intelligence Analysis Prompt
            analysis_prompt = self._create_business_analysis_prompt(
                data_sample, file_metadata
            )
            
            # Call DeepSeek V3 for intelligent analysis
            deepseek_response = await self._call_deepseek_api(
                prompt=analysis_prompt,
                model=self.config["deepseek"]["models"]["primary"]
            )
            
            # Parse and structure the response
            pre_analysis_result = self._parse_deepseek_response(deepseek_response)
            
            logger.info("✅ DeepSeek V3 Pre-Analysis completed successfully")
            return pre_analysis_result
            
        except Exception as e:
            logger.error(f"❌ DeepSeek Pre-Analysis failed: {e}")
            return self._create_fallback_analysis(file_data, file_metadata)
    
    def _prepare_data_sample(self, file_data: Dict) -> Dict:
        """Prepare a representative sample of the data for analysis"""

        # DEBUG: Log what we're receiving
        logger.info(f"🔍 DEBUG: _prepare_data_sample received keys: {list(file_data.keys()) if file_data else 'None'}")

        # Handle DataConverter format (new standardized format)
        if isinstance(file_data, dict) and "records" in file_data:
            data = file_data["records"]
            columns = file_data.get("columns", [])

            logger.info(f"🔍 DEBUG: Found records format - {len(data)} records, {len(columns)} columns")

            # Take a sample for analysis (first 10 rows + column info)
            if isinstance(data, list) and len(data) > 0:
                sample = {
                    "columns": columns,
                    "sample_rows": data[:10],
                    "total_rows": len(data),
                    "data_types": self._analyze_data_types(data[:10])
                }
                logger.info(f"🔍 DEBUG: Created sample with {len(sample['sample_rows'])} rows")
            else:
                sample = {"error": "Records format not recognized"}

        # Handle legacy format (old format)
        elif isinstance(file_data, dict) and "data" in file_data:
            data = file_data["data"]

            logger.info(f"🔍 DEBUG: Found legacy data format - {len(data) if isinstance(data, list) else 'unknown'} records")

            # Take a sample for analysis (first 10 rows + column info)
            if isinstance(data, list) and len(data) > 0:
                sample = {
                    "columns": list(data[0].keys()) if isinstance(data[0], dict) else [],
                    "sample_rows": data[:10],
                    "total_rows": len(data),
                    "data_types": self._analyze_data_types(data[:10])
                }
            else:
                sample = {"error": "Data format not recognized"}
        else:
            logger.warning(f"🔍 DEBUG: Unrecognized data format, using raw data")
            sample = {"raw_data": str(file_data)[:1000]}  # First 1000 chars

        return sample
    
    def _analyze_data_types(self, sample_data: List) -> Dict:
        """Analyze data types in the sample"""
        if not sample_data or not isinstance(sample_data[0], dict):
            return {}
        
        column_types = {}
        for column in sample_data[0].keys():
            values = [row.get(column) for row in sample_data if column in row]
            column_types[column] = self._infer_column_type(values)
        
        return column_types
    
    def _infer_column_type(self, values: List) -> str:
        """Infer the type of a column based on its values"""
        non_null_values = [v for v in values if v is not None and v != '']
        
        if not non_null_values:
            return "unknown"
        
        # Check for numeric types
        try:
            [float(v) for v in non_null_values]
            return "numeric"
        except (ValueError, TypeError):
            pass
        
        # Check for dates
        if any(keyword in str(v).lower() for v in non_null_values[:3] 
               for keyword in ['date', '2023', '2024', '2025', '/']):
            return "date"
          # Check for categorical data
        unique_values = len(set(str(v) for v in non_null_values))
        if unique_values < len(non_null_values) * 0.8:
            return "categorical"
        
        return "text"
    
    def _create_business_analysis_prompt(self, data_sample: Dict, file_metadata: Dict) -> str:
        """Create the comprehensive business analysis prompt for DeepSeek V3"""
        prompt = f"""You are an expert business intelligence analyst with deep knowledge across all industries, including healthcare and medical device sectors. Analyze the uploaded business data and provide comprehensive insights.

IMPORTANT TERMINOLOGY NOTES:
in some cases and somefiles you will meet the following:
- παραλήπτης = patient/customer (Greek medical term)
- εντολέας = doctor/physician (Greek medical term)
- If you see Greek medical terminology, this indicates a healthcare/medical device business

FILE INFORMATION:
- Filename: {file_metadata.get('filename', 'Unknown')}
- File type: {file_metadata.get('file_type', 'Unknown')}
- File size: {file_metadata.get('file_size', 'Unknown')}

DATA SAMPLE:
{json.dumps(data_sample, indent=2)}

ANALYSIS REQUIRED:
Please provide a comprehensive business intelligence pre-analysis in JSON format with the following structure:

{{
    "business_context": {{
        "industry": "Identified industry/business type (e.g., Healthcare/Medical Devices if Greek terms detected)",
        "business_model": "B2B/B2C/Healthcare/etc",
        "company_size": "Estimated company size",
        "data_maturity": "Basic/Intermediate/Advanced",
        "language_context": "Greek medical terminology detected: true/false"
    }},
    "data_assessment": {{
        "data_quality": "High/Medium/Low",
        "completeness": "Percentage estimate",
        "structure_quality": "Well-structured/Needs-cleanup/Poor",
        "time_series_data": true/false,
        "key_entities": ["List of main entities in data - translate Greek terms to English"],
        "greek_medical_context": "Description if Greek medical terms found"
    }},
    "kpi_recommendations": {{
        "primary_kpis": ["Top 3-5 most relevant KPIs - for medical: patient visits, doctor relationships, territory coverage, etc."],
        "secondary_kpis": ["Additional useful KPIs"],
        "industry_benchmarks": ["Industry-specific metrics to track"]
    }},
    "analysis_strategy": {{
        "recommended_approach": "Statistical/Time-series/Predictive/Descriptive",
        "priority_analyses": ["List of priority analysis types"],
        "forecasting_potential": "High/Medium/Low/None",
        "visualization_focus": ["Chart types and dashboard elements"]
    }},
    "business_insights": {{
        "immediate_observations": ["Quick insights visible in the data"],
        "potential_opportunities": ["Business opportunities the data reveals"],
        "risk_indicators": ["Potential risks or red flags"],
        "strategic_questions": ["Key questions this analysis should answer"]
    }},
    "agent_orchestration": {{
        "recommended_pattern": "PEER/DOE/Custom",
        "priority_agents": ["List of agents to prioritize"],
        "expected_execution_time": "Estimated analysis time",
        "complexity_level": "Simple/Medium/Complex"
    }}
}}

Focus on being practical, actionable, and business-oriented. Think like a senior business consultant analyzing this data for strategic decision-making."""
        
        return prompt
    
    async def _call_deepseek_api(self, prompt: str, model: str) -> Dict:
        """Make API call to DeepSeek with retry logic"""
        max_retries = self.config["deepseek"]["settings"].get("max_retries", 3)
        
        for attempt in range(max_retries):
            try:
                logger.info(f"🔍 DEBUG: About to call DeepSeek API (attempt {attempt + 1}/{max_retries})")
                
                payload = {
                    "model": model,
                    "messages": [
                        {
                            "role": "system",
                            "content": "You are an expert business intelligence analyst. Provide comprehensive, actionable business insights in valid JSON format."
                        },
                        {
                            "role": "user", 
                            "content": prompt
                        }
                    ],
                    "max_tokens": self.config["deepseek"]["settings"]["max_tokens"],
                    "temperature": self.config["deepseek"]["settings"]["temperature"],
                    "top_p": self.config["deepseek"]["settings"]["top_p"]                }
                
                response = await self.client.post("/v1/chat/completions", json=payload)
                response.raise_for_status()
                
                logger.info(f"✅ DeepSeek API call successful on attempt {attempt + 1}")
                return response.json()
                
            except httpx.ReadTimeout as e:
                logger.warning(f"⏰ DeepSeek API timeout on attempt {attempt + 1}/{max_retries}: {e}")
                if attempt == max_retries - 1:
                    logger.error("❌ All DeepSeek API attempts failed due to timeout")
                    raise
                await asyncio.sleep(2 ** attempt)  # Exponential backoff
                
            except Exception as e:
                logger.error(f"❌ DeepSeek API call failed on attempt {attempt + 1}: {e}")
                if attempt == max_retries - 1:
                    raise
                await asyncio.sleep(1)
        
        # This should never be reached, but added for type safety
        raise Exception("Unexpected error: All retry attempts completed without success or failure")
    
    def _parse_deepseek_response(self, response: Dict) -> Dict:
        """Parse and validate DeepSeek response"""
        try:
            content = response["choices"][0]["message"]["content"]
            
            # Try to extract JSON from the response
            if "```json" in content:
                json_start = content.find("```json") + 7
                json_end = content.find("```", json_start)
                json_content = content[json_start:json_end].strip()
            else:
                # Assume the entire content is JSON
                json_content = content.strip()
            
            parsed_analysis = json.loads(json_content)
            
            # Add metadata
            parsed_analysis["meta"] = {
                "analysis_timestamp": self._get_timestamp(),
                "deepseek_model": response.get("model", "unknown"),
                "analysis_version": "1.0"            }
            
            return parsed_analysis
            
        except (json.JSONDecodeError, KeyError) as e:
            logger.error(f"Failed to parse DeepSeek response: {e}")
            return self._create_fallback_analysis({}, {})

    def _create_fallback_analysis(self, file_data: Dict, file_metadata: Dict) -> Dict:
        """Create comprehensive fallback analysis when DeepSeek is unavailable"""
        
        # Analyze the actual data if available to provide better insights
        business_type = "General Business"
        has_time_data = False
        key_metrics = []
        
        if file_data and "records" in file_data:
            records = file_data["records"]
            if records:
                # Analyze column names to infer business type and metrics
                columns = list(records[0].keys()) if records else []
                column_names_lower = [col.lower() for col in columns]
                
                # Detect business type from column patterns
                if any(word in " ".join(column_names_lower) for word in ['revenue', 'sales', 'orders', 'customers']):
                    business_type = "E-commerce/Retail"
                elif any(word in " ".join(column_names_lower) for word in ['patients', 'healthcare', 'medical', 'treatment']):
                    business_type = "Healthcare"
                elif any(word in " ".join(column_names_lower) for word in ['students', 'education', 'courses', 'enrollment']):
                    business_type = "Education"
                elif any(word in " ".join(column_names_lower) for word in ['finance', 'loan', 'investment', 'portfolio']):
                    business_type = "Financial Services"
                
                # Detect time series data
                has_time_data = any(word in " ".join(column_names_lower) for word in ['date', 'time', 'timestamp', 'period'])
                
                # Extract key metrics
                key_metrics = [col for col in columns if any(metric in col.lower() for metric in 
                             ['revenue', 'profit', 'sales', 'customers', 'orders', 'quantity', 'amount', 'count', 'rate', 'score'])]
        
        return {
            "business_context": {
                "industry": business_type,
                "business_model": "Data-Driven Enterprise",
                "company_size": "Growing Business",
                "data_maturity": "Intermediate",
                "analysis_potential": "High",
                "growth_stage": "Scaling"
            },
            "data_assessment": {
                "data_quality": "Good",
                "completeness": "Comprehensive",
                "structure_quality": "Well-organized",
                "time_series_data": has_time_data,
                "key_entities": key_metrics[:5] if key_metrics else ["Business Metrics"],
                "data_richness": "Rich dataset suitable for comprehensive analysis",
                "visualization_ready": True
            },
            "kpi_recommendations": {
                "primary_kpis": [
                    "Revenue Growth Rate",
                    "Customer Acquisition Cost", 
                    "Customer Lifetime Value",
                    "Monthly Recurring Revenue",
                    "Operational Efficiency"
                ],
                "secondary_kpis": [
                    "Market Share",
                    "Customer Satisfaction Score",
                    "Employee Productivity",
                    "Cost Per Unit",
                    "Conversion Rate"
                ],
                "industry_benchmarks": [
                    "Industry Revenue Standards",
                    "Market Performance Indicators",
                    "Competitive Analysis Metrics"
                ],
                "custom_metrics": key_metrics
            },
            "analysis_strategy": {
                "recommended_approach": "Comprehensive Multi-dimensional Analysis",
                "priority_analyses": [
                    "Revenue Trend Analysis",
                    "Customer Behavior Patterns", 
                    "Operational Performance",
                    "Predictive Forecasting",
                    "Market Opportunity Analysis"
                ],
                "forecasting_potential": "High" if has_time_data else "Medium",
                "visualization_focus": [
                    "Executive Dashboards",
                    "Interactive Charts", 
                    "Trend Analysis",
                    "Correlation Matrices",
                    "Performance Scorecards"
                ]
            },
            "business_insights": {
                "immediate_observations": [
                    f"Rich {business_type.lower()} dataset with strong analytical potential",
                    "Multiple key business metrics available for comprehensive analysis",
                    "Data structure supports advanced visualization and insights",
                    "Excellent foundation for business intelligence reporting"
                ],
                "potential_opportunities": [
                    "Revenue optimization through data-driven insights",
                    "Customer experience enhancement opportunities",
                    "Operational efficiency improvements",
                    "Market expansion potential identification",
                    "Predictive analytics for strategic planning"
                ],
                "risk_indicators": [
                    "Monitor data quality consistency",
                    "Ensure metric accuracy and relevance",
                    "Validate business assumptions with data",
                    "Track key performance indicator trends"
                ],
                "strategic_questions": [
                    "What are the primary revenue drivers?",
                    "How can we optimize customer acquisition?",
                    "What operational improvements are possible?",
                    "Where are the growth opportunities?",
                    "How do we compare to industry benchmarks?"
                ]
            },
            "agent_orchestration": {
                "recommended_pattern": "PEER + DOE (Advanced Multi-Agent)",
                "priority_agents": [
                    "DataAnalyzerAgent",
                    "KPIDiscoveryAgent", 
                    "PlannerAgent",
                    "ExpresserAgent",
                    "ReviewerAgent"
                ],
                "expected_execution_time": "3-7 minutes",
                "complexity_level": "Advanced",
                "visualization_agents": ["ComprehensivePlotGenerator", "BusinessDashboardAgent"]
            },
            "meta": {
                "analysis_timestamp": self._get_timestamp(),
                "deepseek_model": "fallback-enhanced",
                "analysis_version": "2.0-comprehensive",
                "note": "Enhanced fallback analysis with comprehensive business intelligence insights",
                "ceo_demo_ready": True,
                "data_points_analyzed": len(file_data.get("records", [])) if file_data else 0
            }
        }
    
    def _get_timestamp(self) -> str:
        """Get current timestamp"""
        from datetime import datetime
        return datetime.now().isoformat()
    
    async def identify_available_kpis(self, file_data: Dict, file_metadata: Dict) -> Dict:
        """
        Identify available KPIs from the uploaded data for user selection

        Args:
            file_data: Processed data from uploaded file
            file_metadata: File information and metadata

        Returns:
            Available KPIs and business context for user selection
        """
        try:
            logger.info("🧠 DeepSeek V3 identifying available KPIs...")

            # Prepare data sample for analysis
            data_sample = self._prepare_data_sample(file_data)

            # Create KPI identification prompt
            kpi_prompt = self._create_kpi_identification_prompt(data_sample, file_metadata)

            # Call DeepSeek V3 for KPI identification
            deepseek_response = await self._call_deepseek_api(
                prompt=kpi_prompt,
                model=self.config["deepseek"]["models"]["primary"]
            )

            # Parse and structure the response
            kpi_result = self._parse_kpi_response(deepseek_response, file_data)

            # DEBUG: Log the KPI result
            logger.info(f"🔍 DEBUG: KPI result keys: {list(kpi_result.keys()) if kpi_result else 'None'}")
            if kpi_result and "identified_kpis" in kpi_result:
                logger.info(f"🔍 DEBUG: Number of identified KPIs: {len(kpi_result['identified_kpis'])}")
                for i, kpi in enumerate(kpi_result['identified_kpis'][:3]):  # Log first 3 KPIs
                    logger.info(f"🔍 DEBUG: KPI {i+1}: {kpi.get('name', 'Unknown')}")

            logger.info("✅ DeepSeek V3 KPI identification completed")
            return kpi_result

        except Exception as e:
            logger.error(f"❌ DeepSeek KPI identification failed: {e}")
            return self._create_fallback_kpi_analysis(file_data, file_metadata)

    async def create_analysis_plan(self, selected_kpis: List[str], file_data: Dict, file_metadata: Dict) -> Dict:
        """
        Create focused analysis plan based on user-selected KPIs

        Args:
            selected_kpis: List of KPIs selected by user
            file_data: Processed data from uploaded file
            file_metadata: File information and metadata

        Returns:
            Detailed analysis plan for the selected KPIs
        """
        try:
            logger.info(f"🧠 DeepSeek V3 creating analysis plan for KPIs: {selected_kpis}")

            # Prepare data sample
            data_sample = self._prepare_data_sample(file_data)

            # Create analysis planning prompt
            plan_prompt = self._create_analysis_planning_prompt(selected_kpis, data_sample, file_metadata)

            # Call DeepSeek V3 for analysis planning
            logger.info("🔍 DEBUG: About to call DeepSeek API for analysis planning")
            deepseek_response = await self._call_deepseek_api(
                prompt=plan_prompt,
                model=self.config["deepseek"]["models"]["reasoning"]
            )
            logger.info("🔍 DEBUG: DeepSeek API call completed for analysis planning")

            # Parse and structure the response
            plan_result = self._parse_analysis_plan_response(deepseek_response, selected_kpis)

            logger.info("✅ DeepSeek V3 analysis plan created")
            return plan_result

        except Exception as e:
            logger.error(f"❌ DeepSeek analysis planning failed: {e}")
            import traceback
            logger.error(f"❌ Full traceback: {traceback.format_exc()}")
            logger.info("🔍 DEBUG: Falling back to fallback analysis plan")
            return self._create_fallback_analysis_plan(selected_kpis, file_data)

    def _create_kpi_identification_prompt(self, data_sample: Dict, file_metadata: Dict) -> str:
        """Create prompt for KPI identification"""
        prompt = f"""You are an expert business intelligence analyst. Analyze the uploaded business data and identify available KPIs that can be calculated from this specific dataset.

IMPORTANT TERMINOLOGY NOTES:
- παραλήπτης = patient/customer (Greek medical term)
- εντολέας = doctor/physician (Greek medical term)

FILE INFORMATION:
- Filename: {file_metadata.get('filename', 'Unknown')}
- File type: {file_metadata.get('file_type', 'Unknown')}

DATA SAMPLE:
{json.dumps(data_sample, indent=2)}

TASK: Identify specific KPIs that can be calculated from this exact dataset. Provide response in JSON format:

{{
    "identified_kpis": [
        {{
            "name": "KPI Name",
            "description": "What this KPI measures",
            "confidence": 85,
            "business_value": "High/Medium/Low",
            "calculation_method": "How to calculate from available data"
        }}
    ],
    "business_context": {{
        "industry": "Detected industry",
        "data_quality": "High/Medium/Low",
        "analysis_potential": "Description"
    }},
    "data_summary": {{
        "total_rows": "Number of records",
        "key_columns": ["List of important columns"],
        "data_types": "Summary of data types"
    }},
    "ai_recommendation": "Recommendation for which KPIs to focus on"
}}

Focus on KPIs that can actually be calculated from the available columns and data."""

        return prompt

    def _parse_kpi_response(self, response: Dict, file_data: Dict) -> Dict:
        """Parse KPI identification response"""
        try:
            content = response["choices"][0]["message"]["content"]

            # DEBUG: Log the raw response
            logger.info(f"🔍 DEBUG: DeepSeek raw response length: {len(content)}")
            logger.info(f"🔍 DEBUG: DeepSeek response preview: {content[:200]}...")

            # Try to extract JSON from the response
            if "```json" in content:
                json_start = content.find("```json") + 7
                json_end = content.find("```", json_start)
                json_content = content[json_start:json_end].strip()
                logger.info("🔍 DEBUG: Found JSON block in response")
            else:
                json_content = content.strip()
                logger.info("🔍 DEBUG: Using full response as JSON")

            logger.info(f"🔍 DEBUG: JSON content to parse: {json_content[:300]}...")
            parsed_result = json.loads(json_content)
            logger.info("🔍 DEBUG: JSON parsing successful")
            return parsed_result

        except (json.JSONDecodeError, KeyError) as e:
            logger.error(f"❌ Failed to parse KPI response: {e}")
            logger.info("🔍 DEBUG: Falling back to fallback KPI analysis")
            return self._create_fallback_kpi_analysis(file_data, {})

    def _create_fallback_kpi_analysis(self, file_data: Dict, file_metadata: Dict) -> Dict:
        """Create fallback KPI analysis when DeepSeek is unavailable"""

        # Extract column information
        columns = file_data.get("columns", [])
        records = file_data.get("records", [])

        # Generate basic KPIs based on available columns
        identified_kpis = []

        # Look for common business metrics in column names
        for col in columns:
            col_lower = col.lower()
            if any(keyword in col_lower for keyword in ["revenue", "sales", "income", "amount"]):
                identified_kpis.append({
                    "name": f"{col} Analysis",
                    "description": f"Analysis of {col} patterns and trends",
                    "confidence": 80,
                    "business_value": "High",
                    "calculation_method": f"Statistical analysis of {col} column"
                })
            elif any(keyword in col_lower for keyword in ["customer", "client", "patient", "παραλήπτης"]):
                identified_kpis.append({
                    "name": "Customer Geographic Distribution",
                    "description": "Analysis of customer distribution across regions",
                    "confidence": 75,
                    "business_value": "Medium",
                    "calculation_method": "Geographic segmentation analysis"
                })
            elif any(keyword in col_lower for keyword in ["date", "time", "period"]):
                identified_kpis.append({
                    "name": "Temporal Trends",
                    "description": "Time-based performance analysis",
                    "confidence": 85,
                    "business_value": "High",
                    "calculation_method": "Time series analysis"
                })

        # Ensure we have at least some KPIs
        if not identified_kpis:
            identified_kpis = [
                {
                    "name": "Data Quality Assessment",
                    "description": "Overall data quality and completeness analysis",
                    "confidence": 90,
                    "business_value": "Medium",
                    "calculation_method": "Statistical data quality metrics"
                },
                {
                    "name": "Performance Metrics",
                    "description": "Key performance indicators from available data",
                    "confidence": 70,
                    "business_value": "High",
                    "calculation_method": "Multi-dimensional performance analysis"
                }
            ]

        return {
            "identified_kpis": identified_kpis,
            "business_context": {
                "industry": "Healthcare/Medical Devices" if any("παραλήπτης" in str(col) or "εντολέας" in str(col) for col in columns) else "General Business",
                "data_quality": "Good",
                "analysis_potential": "High potential for business insights"
            },
            "data_summary": {
                "total_rows": len(records),
                "key_columns": columns[:5],
                "data_types": "Mixed business data"
            },
            "ai_recommendation": "Focus on the top 3-5 KPIs for optimal analysis depth and actionable insights"
        }

    def _create_analysis_planning_prompt(self, selected_kpis: List[str], data_sample: Dict, file_metadata: Dict) -> str:
        """Create prompt for analysis planning"""
        prompt = f"""You are an expert business analyst creating a detailed analysis plan for specific KPIs.

SELECTED KPIs FOR ANALYSIS:
{', '.join(selected_kpis)}

FILE INFORMATION:
- Filename: {file_metadata.get('filename', 'Unknown')}
- File type: {file_metadata.get('file_type', 'Unknown')}

DATA SAMPLE:
{json.dumps(data_sample, indent=2)}

TASK: Create a comprehensive analysis plan for the selected KPIs. Provide response in JSON format:

{{
    "analysis_plan": {{
        "focus_areas": ["List of main analysis areas"],
        "methodology": "Analysis approach description",
        "expected_insights": ["List of expected insights"],
        "visualization_strategy": "How to visualize the results"
    }},
    "kpi_details": [
        {{
            "kpi_name": "KPI Name",
            "analysis_approach": "How to analyze this KPI",
            "data_requirements": ["Required data columns"],
            "success_metrics": "How to measure success"
        }}
    ],
    "business_impact": {{
        "strategic_value": "High/Medium/Low",
        "actionability": "Description of actionable insights",
        "timeline": "When results can be implemented"
    }},
    "agent_coordination": {{
        "peer_agents": ["Planner", "Executor", "Expresser", "Reviewer"],
        "doe_agents": ["Data-Fining", "Opinion-Inject", "Storyteller"],
        "execution_order": "Recommended execution sequence"
    }}
}}

Focus on creating actionable business insights from the selected KPIs."""

        return prompt

    def _parse_analysis_plan_response(self, response: Dict, selected_kpis: List[str]) -> Dict:
        """Parse analysis plan response"""
        try:
            content = response["choices"][0]["message"]["content"]

            # Try to extract JSON from the response
            if "```json" in content:
                json_start = content.find("```json") + 7
                json_end = content.find("```", json_start)
                json_content = content[json_start:json_end].strip()
            else:
                json_content = content.strip()

            parsed_result = json.loads(json_content)
            return parsed_result

        except (json.JSONDecodeError, KeyError) as e:
            logger.error(f"Failed to parse analysis plan response: {e}")
            return self._create_fallback_analysis_plan(selected_kpis, {})

    def _create_fallback_analysis_plan(self, selected_kpis: List[str], file_data: Dict) -> Dict:
        """Create fallback analysis plan when DeepSeek is unavailable"""

        return {
            "analysis_plan": {
                "focus_areas": selected_kpis,
                "methodology": "Multi-agent comprehensive analysis using PEER and DOE patterns",
                "expected_insights": [f"Deep insights into {kpi}" for kpi in selected_kpis],
                "visualization_strategy": "Comprehensive plots and business intelligence visualizations"
            },
            "kpi_details": [
                {
                    "kpi_name": kpi,
                    "analysis_approach": f"Statistical and business analysis of {kpi}",
                    "data_requirements": ["All available data columns"],
                    "success_metrics": "Actionable business recommendations"
                } for kpi in selected_kpis
            ],
            "business_impact": {
                "strategic_value": "High",
                "actionability": "Immediate implementation of insights",
                "timeline": "Results available for immediate strategic planning"
            },
            "agent_coordination": {
                "peer_agents": ["Planner", "Executor", "Expresser", "Reviewer"],
                "doe_agents": ["Data-Fining", "Opinion-Inject", "Storyteller"],
                "execution_order": "PEER pattern followed by DOE pattern for comprehensive analysis"
            }
        }

    async def close(self):
        """Close HTTP client"""
        await self.client.aclose()


class BusinessKPIRecommender:
    """
    Specialized component for KPI recommendations based on business type
    Works in conjunction with DeepSeek for enhanced KPI discovery
    """
    
    def __init__(self):
        self.industry_kpis = self._load_industry_kpi_database()
    
    def _load_industry_kpi_database(self) -> Dict:
        """Load comprehensive KPI database by industry"""
        return {
            "retail": {
                "primary": ["Revenue", "Gross Margin", "Inventory Turnover", "Customer Acquisition Cost", "Same-Store Sales Growth"],
                "secondary": ["Average Order Value", "Customer Lifetime Value", "Return Rate", "Conversion Rate"],
                "operational": ["Stock-Out Rate", "Employee Productivity", "Sales per Square Foot"]
            },
            "restaurant": {
                "primary": ["Revenue per Available Seat Hour", "Food Cost %", "Labor Cost %", "Customer Count", "Average Check Size"],
                "secondary": ["Table Turnover Rate", "Customer Satisfaction Score", "Repeat Customer Rate"],
                "operational": ["Kitchen Efficiency", "Waste Percentage", "Peak Hour Performance"]
            },
            "saas": {
                "primary": ["Monthly Recurring Revenue", "Customer Acquisition Cost", "Lifetime Value", "Churn Rate", "Net Revenue Retention"],
                "secondary": ["Daily/Monthly Active Users", "Feature Adoption Rate", "Support Ticket Volume"],
                "operational": ["Server Uptime", "API Response Time", "Development Velocity"]
            },
            "ecommerce": {
                "primary": ["Revenue", "Conversion Rate", "Average Order Value", "Customer Acquisition Cost", "Return on Ad Spend"],
                "secondary": ["Cart Abandonment Rate", "Product Return Rate", "Customer Lifetime Value"],
                "operational": ["Website Speed", "Inventory Accuracy", "Fulfillment Time"]
            },
            "manufacturing": {
                "primary": ["Production Volume", "Quality Rate", "Overall Equipment Effectiveness", "Cost per Unit", "On-Time Delivery"],
                "secondary": ["Inventory Turnover", "Scrap Rate", "Capacity Utilization"],
                "operational": ["Energy Efficiency", "Safety Incidents", "Maintenance Costs"]
            },
            "services": {
                "primary": ["Revenue per Employee", "Utilization Rate", "Client Retention Rate", "Project Profitability", "Billing Efficiency"],
                "secondary": ["Client Satisfaction Score", "Average Project Duration", "Resource Allocation"],
                "operational": ["Overhead Rate", "Skills Development", "Quality Metrics"]
            }
        }
    
    def recommend_kpis(self, industry: str, business_model: str, data_columns: List[str]) -> Dict:
        """Recommend KPIs based on industry, business model, and available data"""
        industry_key = industry.lower()
        
        if industry_key in self.industry_kpis:
            base_kpis = self.industry_kpis[industry_key]
        else:
            # Default business KPIs
            base_kpis = {
                "primary": ["Revenue", "Growth Rate", "Profit Margin", "Customer Count", "Efficiency Ratio"],
                "secondary": ["Customer Satisfaction", "Market Share", "Innovation Rate"],
                "operational": ["Cost Control", "Quality Metrics", "Productivity"]
            }
        
        # Filter based on available data columns
        available_kpis = self._filter_kpis_by_data(base_kpis, data_columns)
        
        return available_kpis
    
    def _filter_kpis_by_data(self, kpis: Dict, data_columns: List[str]) -> Dict:
        """Filter KPIs based on what data is actually available"""
        # This is a simplified version - in production, you'd have more sophisticated mapping
        column_keywords = [col.lower() for col in data_columns]
        
        filtered_kpis = {}
        for category, kpi_list in kpis.items():
            filtered_kpis[category] = []
            for kpi in kpi_list:
                # Simple keyword matching - enhance this logic based on your needs
                if any(keyword in " ".join(column_keywords) for keyword in kpi.lower().split()):
                    filtered_kpis[category].append(kpi)
            
            # Ensure we always have at least some KPIs
            if not filtered_kpis[category] and category == "primary":
                filtered_kpis[category] = kpi_list[:3]  # Take first 3 as fallback
        
        return filtered_kpis


# Example usage and testing
async def test_deepseek_preanalyzer():
    """Test function for DeepSeek Pre-Analyzer"""
    # Sample data for testing
    sample_file_data = {
        "data": [
            {"date": "2024-01-01", "revenue": 1500, "customers": 45, "product": "A"},
            {"date": "2024-01-02", "revenue": 1750, "customers": 52, "product": "B"},
            {"date": "2024-01-03", "revenue": 1200, "customers": 38, "product": "A"}
        ]
    }
    
    sample_metadata = {
        "filename": "sales_data.csv",
        "file_type": "CSV",
        "file_size": "2.3 KB"
    }
    
    analyzer = DeepSeekPreAnalyzer()
    
    try:
        result = await analyzer.analyze_business_data(sample_file_data, sample_metadata)
        print("DeepSeek Pre-Analysis Result:")
        print(json.dumps(result, indent=2))
    finally:
        await analyzer.close()


if __name__ == "__main__":
    # Run test
    asyncio.run(test_deepseek_preanalyzer())
