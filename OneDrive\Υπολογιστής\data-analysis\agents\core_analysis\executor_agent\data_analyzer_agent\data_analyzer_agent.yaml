# AgentUniverse Configuration for DataAnalyzerAgent
name: "DataAnalyzerAgent"
description: "Performs statistical analysis and correlations on business data as part of the PEER Execute phase"
agent_type: "react"

class_name: "data_analyzer_agent.DataAnalyzerAgent"

llm:
  type: "deepseek_llm"
  model: "deepseek-r1" # Reasoning-intensive model for statistical analysis
  # llm_config_name: "deepseek_reasoning_config"

prompt:
  version: "data_analyzer_v1"
  # Template for statistical analysis prompt
  # prompt_template: |
  #   You are an expert data analyst specializing in business statistics and correlations.
  #   
  #   Analysis Plan: {analysis_plan}
  #   Data Summary: {data_summary}
  #   Business Context: {business_context}
  #   Target KPIs: {target_kpis}
  #   
  #   Perform comprehensive statistical analysis including:
  #   1. Correlation analysis between key variables
  #   2. Distribution analysis of numerical columns
  #   3. Trend identification over time periods
  #   4. Outlier detection and significance
  #   5. Statistical significance tests where appropriate
  #   6. Business-relevant insights and patterns
  #   
  #   Output your analysis in JSON format with the following structure:
  #   {
  #     "correlations": {...},
  #     "distributions": {...},
  #     "trends": {...},
  #     "outliers": {...},
  #     "statistical_tests": {...},
  #     "business_insights": [...],
  #     "recommended_kpis": [...],
  #     "data_quality_assessment": {...}
  #   }

output_parser:
  class_name: "data_analyzer_agent.DataAnalyzerAgentOutputParser"

# Input and output examples
# input_keys: ["analysis_plan", "data_summary", "business_context", "target_kpis"]
# output_keys: ["correlations", "distributions", "trends", "outliers", "business_insights"]
