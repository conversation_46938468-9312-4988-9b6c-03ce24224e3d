#!/usr/bin/env python3
"""
Test the web interface routes to ensure they're working
"""

import requests
import json

def test_routes():
    """Test the web interface routes"""
    base_url = "http://localhost:8000"
    
    print("🧪 Testing web interface routes...")
    
    # Test 1: Health check
    try:
        response = requests.get(f"{base_url}/api/health")
        print(f"✅ Health check: {response.status_code} - {response.json()}")
    except Exception as e:
        print(f"❌ Health check failed: {e}")
    
    # Test 2: Status endpoint
    try:
        response = requests.get(f"{base_url}/status")
        print(f"✅ Status endpoint: {response.status_code}")
        if response.status_code == 200:
            status = response.json()
            print(f"   Platform: {status.get('platform_name')}")
            print(f"   Version: {status.get('version')}")
            print(f"   Status: {status.get('status')}")
    except Exception as e:
        print(f"❌ Status endpoint failed: {e}")
    
    # Test 3: Main page
    try:
        response = requests.get(f"{base_url}/")
        print(f"✅ Main page: {response.status_code}")
    except Exception as e:
        print(f"❌ Main page failed: {e}")
    
    print("🎉 Route testing completed!")

if __name__ == "__main__":
    test_routes()
