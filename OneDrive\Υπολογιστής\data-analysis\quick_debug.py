#!/usr/bin/env python3
"""
Quick test to see what the platform returns
"""

import asyncio
import json
import os
from main_platform import AIDataAnalysisPlatform

async def quick_test():
    """Quick test of platform response"""
    print("🔍 Quick response test...")
    
    platform = AIDataAnalysisPlatform()
    
    # Use test data instead
    print("📁 Creating test data...")
    import pandas as pd
    
    # Create simple test data
    test_data = pd.DataFrame({
        'Sales': [100, 150, 200, 120, 180],
        'Region': ['North', 'South', 'East', 'West', 'Central'],
        'Date': pd.date_range('2024-01-01', periods=5),
        'Revenue': [1000, 1500, 2000, 1200, 1800]
    })
    
    # Save to CSV for testing
    test_data.to_csv('quick_test.csv', index=False)
    
    # Read and analyze
    with open('quick_test.csv', 'rb') as f:
        content = f.read()
    
    print("🚀 Running analysis...")
    result = await platform.analyze_business_data('quick_test.csv', content)
    
    print("\n📊 RESULT STRUCTURE:")
    print(f"Type: {type(result)}")
    
    if isinstance(result, dict):
        print("Top-level keys:")
        for key in result.keys():
            print(f"  - {key}")
            
        # Look specifically for visualization data
        if 'agent_orchestration' in result:
            print("\n🎨 Agent orchestration keys:")
            orch = result['agent_orchestration']
            if isinstance(orch, dict):
                for key in orch.keys():
                    print(f"  - {key}")
                    
                if 'visualization_results' in orch:
                    viz = orch['visualization_results']
                    print(f"\n✅ Found visualization_results: {type(viz)}")
                    if isinstance(viz, dict):
                        print("Visualization keys:")
                        for key in viz.keys():
                            print(f"  - {key}")
                            if key == 'total_plots':
                                print(f"    Total plots: {viz[key]}")
                            elif key == 'plots' and viz[key]:
                                print(f"    Plots available: {len(viz[key])}")
                                print(f"    First 3 plot names: {list(viz[key].keys())[:3]}")
    
    # Clean up
    if os.path.exists('quick_test.csv'):
        os.remove('quick_test.csv')

if __name__ == "__main__":
    asyncio.run(quick_test())
