#!/usr/bin/env python3
"""
MINIMAL TEST WEB INTERFACE - To verify if the issue is with the main web interface
"""

from fastapi import Fast<PERSON><PERSON>
from fastapi.responses import HTMLResponse

app = FastAPI()

@app.get("/", response_class=HTMLResponse)
async def test_page():
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>🧪 MINIMAL TEST INTERFACE</title>
    </head>
    <body>
        <h1>🧪 MINIMAL TEST - THIS SHOULD BE VISIBLE</h1>
        <p>If you can see this, then the web serving works.</p>
        <p>If you can't see this, there's a fundamental issue.</p>
    </body>
    </html>
    """

if __name__ == "__main__":
    print("🧪 Starting MINIMAL test web interface...")
    print("📍 Access at: http://localhost:8002")
    
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002)
