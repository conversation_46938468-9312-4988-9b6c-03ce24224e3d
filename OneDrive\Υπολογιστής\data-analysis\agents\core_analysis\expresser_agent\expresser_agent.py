"""
Expresser Agent for the AI Data Analysis Platform.
This agent creates comprehensive reports and visualizations from analysis results.
"""
import json
from typing import Any, Dict, Optional, List
from agentuniverse.agent.agent import Agent


class ExpresserAgent(Agent):
    """PEER Pattern - Expression Agent for Report Generation"""

    def __init__(self, **kwargs):
        """Initialize the Expresser Agent"""
        super().__init__(**kwargs)
        
    def input_keys(self) -> list[str]:
        """Required input keys for this agent"""
        return ["analysis_results", "kpi_results", "forecast_results", "business_context", "user_requirements"]
    
    def output_keys(self) -> list[str]:
        """Output keys this agent produces"""
        return ["comprehensive_report", "executive_summary", "visualizations", "recommendations", "formatted_output"]
    
    def parse_input(self, input_object, agent_input):
        """Parse input for the agent"""
        return {
            "analysis_results": agent_input.get("analysis_results", {}),
            "kpi_results": agent_input.get("kpi_results", {}),
            "forecast_results": agent_input.get("forecast_results", {}),
            "business_context": agent_input.get("business_context", {}),
            "user_requirements": agent_input.get("user_requirements", {})
        }
    
    def parse_result(self, agent_result):
        """Parse agent result"""
        return agent_result

    def execute(self, input_object, agent_input):
        """
        Generate comprehensive business intelligence reports and visualizations.
        """
        analysis_results = agent_input.get("analysis_results", {})
        kpi_results = agent_input.get("kpi_results", {})
        forecast_results = agent_input.get("forecast_results", {})
        business_context = agent_input.get("business_context", {})
        user_requirements = agent_input.get("user_requirements", {})

        try:
            # Generate executive summary
            executive_summary = self._generate_executive_summary(
                analysis_results, kpi_results, forecast_results, business_context
            )
            
            # Create comprehensive report
            comprehensive_report = self._create_comprehensive_report(
                analysis_results, kpi_results, forecast_results, business_context
            )
            
            # Generate visualizations configuration
            visualizations = self._generate_visualizations_config(
                analysis_results, kpi_results, forecast_results
            )
            
            # Create recommendations
            recommendations = self._generate_recommendations(
                analysis_results, kpi_results, forecast_results, business_context
            )
            
            # Format output for different channels
            formatted_output = self._format_output_channels(
                comprehensive_report, executive_summary, visualizations, recommendations
            )

            return {
                "comprehensive_report": comprehensive_report,
                "executive_summary": executive_summary,
                "visualizations": visualizations,
                "recommendations": recommendations,
                "formatted_output": formatted_output,
                "success": True
            }

        except Exception as e:
            return {
                "error": f"Report generation failed: {str(e)}",
                "success": False
            }

    def _generate_executive_summary(self, analysis_results: Dict, kpi_results: Dict, 
                                  forecast_results: Dict, business_context: Dict) -> Dict[str, Any]:
        """Generate executive summary of key findings"""
        summary = {
            "overview": "",
            "key_findings": [],
            "critical_insights": [],
            "business_impact": "",
            "next_steps": []
        }
        
        # Extract key metrics
        key_metrics = []
        if kpi_results.get("discovered_kpis"):
            for kpi_name, kpi_data in kpi_results["discovered_kpis"].items():
                if isinstance(kpi_data, dict) and "value" in kpi_data:
                    key_metrics.append(f"{kpi_name}: {kpi_data['value']}")
        
        # Generate overview
        business_type = business_context.get("business_type", "business")
        dataset_info = analysis_results.get("dataset_info", {})
        
        if dataset_info:
            shape = dataset_info.get("shape", [0, 0])
            summary["overview"] = (
                f"Analysis of {business_type} data containing {shape[0]:,} records "
                f"across {shape[1]} variables. "
                f"Key performance indicators and trends have been identified."
            )
        
        # Extract key findings
        if analysis_results.get("insights"):
            summary["key_findings"] = analysis_results["insights"][:3]  # Top 3 insights
        
        # Critical insights from correlations and patterns
        if analysis_results.get("correlations", {}).get("strong_correlations"):
            correlations = analysis_results["correlations"]["strong_correlations"]
            if correlations:
                summary["critical_insights"].append(
                    f"Strong correlations identified between {len(correlations)} variable pairs"
                )
        
        # Business impact from KPIs
        if key_metrics:
            summary["business_impact"] = f"Key metrics tracked: {', '.join(key_metrics[:3])}"
        
        # Next steps
        summary["next_steps"] = [
            "Monitor identified KPIs for trend changes",
            "Implement data quality improvements where needed",
            "Set up automated reporting for key metrics"
        ]
        
        if forecast_results.get("predictions"):
            summary["next_steps"].append("Review forecasting accuracy and adjust models")
        
        return summary

    def _create_comprehensive_report(self, analysis_results: Dict, kpi_results: Dict,
                                   forecast_results: Dict, business_context: Dict) -> Dict[str, Any]:
        """Create detailed comprehensive report"""
        report = {
            "metadata": {
                "report_type": "Business Intelligence Analysis",
                "generated_at": "2025-06-11",  # Would use actual timestamp
                "business_context": business_context
            },
            "sections": {}
        }
        
        # Data Overview Section
        if analysis_results.get("dataset_info"):
            report["sections"]["data_overview"] = {
                "title": "Data Overview",
                "content": analysis_results["dataset_info"],
                "summary": self._create_data_overview_summary(analysis_results["dataset_info"])
            }
        
        # Statistical Analysis Section
        if analysis_results.get("statistical_summary"):
            report["sections"]["statistical_analysis"] = {
                "title": "Statistical Analysis",
                "content": analysis_results["statistical_summary"],
                "summary": self._create_statistical_summary(analysis_results["statistical_summary"])
            }
        
        # KPI Analysis Section
        if kpi_results.get("discovered_kpis"):
            report["sections"]["kpi_analysis"] = {
                "title": "Key Performance Indicators",
                "content": kpi_results,
                "summary": self._create_kpi_summary(kpi_results)
            }
        
        # Data Quality Section
        if analysis_results.get("data_quality"):
            report["sections"]["data_quality"] = {
                "title": "Data Quality Assessment",
                "content": analysis_results["data_quality"],
                "summary": self._create_data_quality_summary(analysis_results["data_quality"])
            }
        
        # Forecasting Section
        if forecast_results.get("predictions"):
            report["sections"]["forecasting"] = {
                "title": "Forecasting & Predictions",
                "content": forecast_results,
                "summary": self._create_forecast_summary(forecast_results)
            }
        
        # Patterns and Correlations Section
        if analysis_results.get("patterns") or analysis_results.get("correlations"):
            report["sections"]["patterns_correlations"] = {
                "title": "Patterns & Correlations",
                "content": {
                    "patterns": analysis_results.get("patterns", {}),
                    "correlations": analysis_results.get("correlations", {})
                },
                "summary": self._create_patterns_summary(analysis_results)
            }
        
        return report

    def _generate_visualizations_config(self, analysis_results: Dict, kpi_results: Dict,
                                      forecast_results: Dict) -> List[Dict[str, Any]]:
        """Generate configuration for visualizations"""
        visualizations = []
        
        # KPI Dashboard
        if kpi_results.get("discovered_kpis"):
            kpi_viz = {
                "type": "kpi_dashboard",
                "title": "Key Performance Indicators",
                "config": {
                    "kpis": kpi_results["discovered_kpis"],
                    "layout": "grid",
                    "show_trends": True
                }
            }
            visualizations.append(kpi_viz)
        
        # Statistical Summary Charts
        if analysis_results.get("statistical_summary", {}).get("numerical"):
            stats_viz = {
                "type": "statistical_summary",
                "title": "Statistical Overview",
                "config": {
                    "data": analysis_results["statistical_summary"]["numerical"],
                    "chart_types": ["histogram", "box_plot"],
                    "show_outliers": True
                }
            }
            visualizations.append(stats_viz)
        
        # Correlation Heatmap
        if analysis_results.get("correlations", {}).get("correlation_matrix"):
            corr_viz = {
                "type": "correlation_heatmap",
                "title": "Variable Correlations",
                "config": {
                    "correlation_matrix": analysis_results["correlations"]["correlation_matrix"],
                    "color_scheme": "diverging",
                    "show_values": True
                }
            }
            visualizations.append(corr_viz)
        
        # Time Series Forecast
        if forecast_results.get("predictions"):
            forecast_viz = {
                "type": "time_series_forecast",
                "title": "Forecasting Results",
                "config": {
                    "predictions": forecast_results["predictions"],
                    "historical_data": forecast_results.get("historical_values", []),
                    "confidence_intervals": forecast_results.get("confidence_intervals", {}),
                    "show_trend": True
                }
            }
            visualizations.append(forecast_viz)
        
        # Data Quality Summary
        if analysis_results.get("data_quality"):
            quality_viz = {
                "type": "data_quality_summary",
                "title": "Data Quality Assessment",
                "config": {
                    "missing_data": analysis_results["data_quality"].get("missing_values", {}),
                    "duplicates": analysis_results["data_quality"].get("duplicates", {}),
                    "chart_type": "bar_chart"
                }
            }
            visualizations.append(quality_viz)
        
        return visualizations

    def _generate_recommendations(self, analysis_results: Dict, kpi_results: Dict,
                                forecast_results: Dict, business_context: Dict) -> List[Dict[str, Any]]:
        """Generate actionable recommendations"""
        recommendations = []
        
        # Data Quality Recommendations
        data_quality = analysis_results.get("data_quality", {})
        missing_values = data_quality.get("missing_values", {})
        
        if missing_values.get("total_missing", 0) > 0:
            recommendations.append({
                "category": "Data Quality",
                "priority": "High",
                "title": "Address Missing Data",
                "description": f"Found {missing_values['total_missing']} missing values. Consider data cleaning and validation processes.",
                "impact": "Improved analysis accuracy",
                "effort": "Medium"
            })
        
        # KPI Recommendations
        if kpi_results.get("recommendations"):
            for rec in kpi_results["recommendations"]:
                recommendations.append({
                    "category": "KPI Management",
                    "priority": "Medium",
                    "title": "KPI Enhancement",
                    "description": rec,
                    "impact": "Better business insights",
                    "effort": "Low"
                })
        
        # Correlation-based Recommendations
        strong_correlations = analysis_results.get("correlations", {}).get("strong_correlations", [])
        if strong_correlations:
            recommendations.append({
                "category": "Analysis Deep-dive",
                "priority": "Medium",
                "title": "Investigate Strong Correlations",
                "description": f"Explore the {len(strong_correlations)} strong correlations to understand causal relationships.",
                "impact": "Enhanced predictive capabilities",
                "effort": "Medium"
            })
        
        # Forecasting Recommendations
        if forecast_results.get("predictions"):
            recommendations.append({
                "category": "Planning",
                "priority": "High",
                "title": "Implement Forecasting Insights",
                "description": "Use forecasting results for strategic planning and resource allocation.",
                "impact": "Improved decision making",
                "effort": "High"
            })
        
        return recommendations

    def _format_output_channels(self, report: Dict, summary: Dict, visualizations: List,
                              recommendations: List) -> Dict[str, Any]:
        """Format output for different channels (dashboard, email, PDF, etc.)"""
        return {
            "dashboard": {
                "summary_cards": self._create_summary_cards(summary),
                "main_visualizations": visualizations[:3],  # Top 3 visualizations
                "key_metrics": self._extract_key_metrics(report)
            },
            "email_report": {
                "subject": "Business Intelligence Analysis Report",
                "body": self._create_email_body(summary, recommendations),
                "attachments": ["full_report.pdf", "data_export.csv"]
            },
            "pdf_export": {
                "sections": report["sections"],
                "executive_summary": summary,
                "visualizations": visualizations,
                "recommendations": recommendations
            },
            "api_response": {
                "status": "completed",
                "summary": summary,
                "report": report,
                "visualizations": visualizations,
                "recommendations": recommendations
            }
        }

    def _create_data_overview_summary(self, dataset_info: Dict) -> str:
        """Create summary for data overview section"""
        shape = dataset_info.get("shape", [0, 0])
        return f"Dataset contains {shape[0]:,} records across {shape[1]} variables with {dataset_info.get('memory_usage', 0)} bytes memory usage."

    def _create_statistical_summary(self, stats: Dict) -> str:
        """Create summary for statistical analysis section"""
        numerical_count = len(stats.get("numerical", {}))
        categorical_count = len(stats.get("categorical", {}))
        return f"Analysis covers {numerical_count} numerical and {categorical_count} categorical variables."

    def _create_kpi_summary(self, kpi_results: Dict) -> str:
        """Create summary for KPI section"""
        kpi_count = len(kpi_results.get("discovered_kpis", {}))
        return f"Identified {kpi_count} key performance indicators relevant to business objectives."

    def _create_data_quality_summary(self, quality: Dict) -> str:
        """Create summary for data quality section"""
        missing = quality.get("missing_values", {}).get("total_missing", 0)
        duplicates = quality.get("duplicates", {}).get("duplicate_rows", 0)
        return f"Data quality assessment: {missing} missing values, {duplicates} duplicate records."

    def _create_forecast_summary(self, forecast: Dict) -> str:
        """Create summary for forecasting section"""
        periods = len(forecast.get("predictions", []))
        method = forecast.get("method", "unknown")
        return f"Generated {periods} period forecast using {method} methodology."

    def _create_patterns_summary(self, analysis: Dict) -> str:
        """Create summary for patterns and correlations section"""
        patterns = analysis.get("patterns", {})
        correlations = analysis.get("correlations", {}).get("strong_correlations", [])
        return f"Identified patterns in data and {len(correlations)} strong correlations between variables."

    def _create_summary_cards(self, summary: Dict) -> List[Dict[str, str]]:
        """Create summary cards for dashboard"""
        return [
            {"title": "Overview", "content": summary.get("overview", "")},
            {"title": "Business Impact", "content": summary.get("business_impact", "")},
            {"title": "Key Findings", "content": "; ".join(summary.get("key_findings", [])[:2])}
        ]

    def _extract_key_metrics(self, report: Dict) -> List[Dict[str, Any]]:
        """Extract key metrics for dashboard display"""
        metrics = []
        kpi_section = report.get("sections", {}).get("kpi_analysis", {}).get("content", {})
        
        if kpi_section.get("discovered_kpis"):
            for kpi_name, kpi_data in list(kpi_section["discovered_kpis"].items())[:5]:
                if isinstance(kpi_data, dict) and "value" in kpi_data:
                    metrics.append({
                        "name": kpi_name,
                        "value": kpi_data["value"],
                        "type": kpi_data.get("type", "metric")
                    })
        
        return metrics

    def _create_email_body(self, summary: Dict, recommendations: List) -> str:
        """Create email body content"""
        body = f"""
Business Intelligence Analysis Summary

{summary.get('overview', '')}

Key Findings:
{chr(10).join('• ' + finding for finding in summary.get('key_findings', []))}

Top Recommendations:
{chr(10).join('• ' + rec.get('title', '') + ': ' + rec.get('description', '') for rec in recommendations[:3])}

Please find the detailed report attached.
"""
        return body.strip()
