# AgentUniverse Configuration for CodeExecutorAgent
name: "CodeExecutorAgent"
description: "Executes Python code snippets, often provided by other agents, in a controlled environment."
agent_type: "react" # Or "singleton" or other relevant AgentUniverse agent type

class_name: "agents.core_analysis.executor_agent.code_executor_agent.code_executor_agent.CodeExecutorAgent"

llm:
  type: "deepseek_llm" # This agent might not always need an LLM, or use it for code validation/explanation
  # llm_config_name: "deepseek_chat_config"

# This agent might not directly use a prompt for LLM interaction in its core execution logic
# but could have prompts for auxiliary tasks (e.g., explaining execution errors).
# prompt:
#   version: "0.0.1"

output_parser:
  class_name: "code_executor_agent.CodeExecutorAgentOutputParser"

# Potentially tools for sandboxed code execution if not built into the agent's core logic
# tools:
#   - name: "python_interpreter_tool"
#     class_name: "path.to.your.PythonInterpreterTool"

# input_keys: ["code_to_execute", "execution_context"]
# output_keys: ["execution_result", "stdout", "stderr", "error"]
