#!/usr/bin/env python3
"""
Detailed diagnostic test
"""

import sys
import traceback
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

print("🔬 Detailed Diagnostic Test")
print("=" * 50)

# Test CodeExecutorAgent module loading
print("\n📁 Testing CodeExecutorAgent module...")
try:
    import agents.core_analysis.executor_agent.code_executor_agent.code_executor_agent as code_executor_module
    print(f"✅ Module loaded: {code_executor_module}")
    print(f"📋 Module attributes: {dir(code_executor_module)}")
    
    if hasattr(code_executor_module, 'CodeExecutorAgent'):
        print("✅ CodeExecutorAgent class found!")
        agent = code_executor_module.CodeExecutorAgent()
        print("✅ CodeExecutorAgent instantiated!")
    else:
        print("❌ CodeExecutorAgent class NOT FOUND in module")
        
except Exception as e:
    print(f"❌ Module loading failed: {e}")
    traceback.print_exc()

# Test StorytellerAgent module loading  
print("\n📁 Testing StorytellerAgent module...")
try:
    import agents.specialized_business.storyteller_agent.storyteller_agent as storyteller_module
    print(f"✅ Module loaded: {storyteller_module}")
    print(f"📋 Module attributes: {dir(storyteller_module)}")
    
    if hasattr(storyteller_module, 'StorytellerAgent'):
        print("✅ StorytellerAgent class found!")
        agent = storyteller_module.StorytellerAgent()
        print("✅ StorytellerAgent instantiated!")
    else:
        print("❌ StorytellerAgent class NOT FOUND in module")
        
except Exception as e:
    print(f"❌ Module loading failed: {e}")
    traceback.print_exc()

print("\n✅ Diagnostic completed")
