"""
OpinionInjectAgent: Injects business opinions and domain expertise as part of the DOE Opinion-inject phase.
"""
from agentuniverse.agent.agent import Agent
from typing import Any, Dict, List
import json

class OpinionInjectAgent(Agent):
    """Injects business domain expertise and strategic opinions into analysis results."""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def input_keys(self) -> list[str]:
        return ["analysis_results", "business_domain", "strategic_context", "expert_knowledge"]

    def output_keys(self) -> list[str]:
        return ["enriched_insights", "strategic_recommendations", "risk_assessments", "opportunity_analysis"]

    def parse_input(self, input_object, agent_input):
        return {
            "analysis_results": agent_input.get("analysis_results", {}),
            "business_domain": agent_input.get("business_domain", ""),
            "strategic_context": agent_input.get("strategic_context", {}),
            "expert_knowledge": agent_input.get("expert_knowledge", {})
        }

    def parse_result(self, agent_result):
        return agent_result

    def execute(self, input_object, agent_input):
        """
        Inject business expertise and strategic opinions into analysis results.
        """
        analysis_results = agent_input.get("analysis_results", {})
        business_domain = agent_input.get("business_domain", "")
        strategic_context = agent_input.get("strategic_context", {})
        expert_knowledge = agent_input.get("expert_knowledge", {})

        if not analysis_results:
            return {"error": "No analysis results provided", "success": False}

        try:
            # Inject domain-specific insights
            enriched_insights = self._inject_domain_insights(analysis_results, business_domain, expert_knowledge)
            
            # Generate strategic recommendations
            strategic_recommendations = self._generate_strategic_recommendations(
                analysis_results, business_domain, strategic_context
            )
            
            # Assess risks and opportunities
            risk_assessments = self._assess_risks(analysis_results, business_domain, strategic_context)
            opportunity_analysis = self._analyze_opportunities(analysis_results, business_domain, strategic_context)

            return {
                "enriched_insights": enriched_insights,
                "strategic_recommendations": strategic_recommendations,
                "risk_assessments": risk_assessments,
                "opportunity_analysis": opportunity_analysis,
                "success": True
            }

        except Exception as e:
            return {
                "error": f"Opinion injection failed: {str(e)}",
                "success": False
            }

    def _inject_domain_insights(self, results: Dict[str, Any], domain: str, 
                              expert_knowledge: Dict[str, Any]) -> Dict[str, Any]:
        """Inject domain-specific business insights"""
        domain_insights = {
            "contextual_interpretations": [],
            "industry_benchmarks": {},
            "best_practices": [],
            "expert_opinions": []
        }

        # Domain-specific interpretations
        if domain.lower() in ['retail', 'ecommerce']:
            domain_insights.update(self._inject_retail_insights(results, expert_knowledge))
        elif domain.lower() in ['finance', 'financial', 'banking']:
            domain_insights.update(self._inject_financial_insights(results, expert_knowledge))
        elif domain.lower() in ['manufacturing', 'production']:
            domain_insights.update(self._inject_manufacturing_insights(results, expert_knowledge))
        elif domain.lower() in ['technology', 'software', 'saas']:
            domain_insights.update(self._inject_technology_insights(results, expert_knowledge))
        else:
            domain_insights.update(self._inject_general_business_insights(results, expert_knowledge))

        return domain_insights

    def _inject_retail_insights(self, results: Dict[str, Any], expert_knowledge: Dict[str, Any]) -> Dict[str, Any]:
        """Inject retail-specific insights"""
        insights = {
            "contextual_interpretations": [
                "Retail performance should be evaluated against seasonal trends",
                "Customer acquisition cost vs lifetime value is critical",
                "Inventory turnover rates indicate operational efficiency"
            ],
            "industry_benchmarks": {
                "average_conversion_rate": "2-3%",
                "customer_retention_rate": "60-70%",
                "inventory_turnover": "4-6 times annually"
            },
            "best_practices": [
                "Focus on omnichannel customer experience",
                "Implement dynamic pricing strategies",
                "Optimize supply chain for demand fluctuations"
            ],
            "expert_opinions": []
        }

        # Add specific opinions based on KPI results
        if "discovered_kpis" in results:
            kpis = results["discovered_kpis"]
            if isinstance(kpis, dict):
                revenue_kpis = [k for k in kpis.keys() if 'revenue' in k.lower() or 'sales' in k.lower()]
                if revenue_kpis:
                    insights["expert_opinions"].append(
                        "Revenue trends in retail are heavily influenced by seasonality and market conditions"
                    )

        return insights

    def _inject_financial_insights(self, results: Dict[str, Any], expert_knowledge: Dict[str, Any]) -> Dict[str, Any]:
        """Inject finance-specific insights"""
        insights = {
            "contextual_interpretations": [
                "Financial metrics must be viewed in context of economic cycles",
                "Risk-adjusted returns are more meaningful than absolute returns",
                "Liquidity ratios indicate financial health and operational flexibility"
            ],
            "industry_benchmarks": {
                "roi_target": "8-12%",
                "debt_to_equity_ratio": "0.3-0.6",
                "current_ratio": "1.2-2.0"
            },
            "best_practices": [
                "Maintain diversified revenue streams",
                "Monitor regulatory compliance metrics",
                "Implement robust risk management frameworks"
            ],
            "expert_opinions": [
                "Financial performance should be evaluated against peer benchmarks",
                "Long-term sustainability requires balanced growth and profitability"
            ]
        }

        return insights

    def _inject_manufacturing_insights(self, results: Dict[str, Any], expert_knowledge: Dict[str, Any]) -> Dict[str, Any]:
        """Inject manufacturing-specific insights"""
        insights = {
            "contextual_interpretations": [
                "Manufacturing efficiency is measured by OEE (Overall Equipment Effectiveness)",
                "Quality metrics directly impact customer satisfaction and costs",
                "Supply chain disruptions can significantly affect production metrics"
            ],
            "industry_benchmarks": {
                "oee_target": "85%+",
                "defect_rate": "<1%",
                "on_time_delivery": "95%+"
            },
            "best_practices": [
                "Implement lean manufacturing principles",
                "Focus on predictive maintenance",
                "Optimize production scheduling and capacity utilization"
            ],
            "expert_opinions": [
                "Continuous improvement culture is essential for manufacturing excellence",
                "Technology integration drives operational efficiency"
            ]
        }

        return insights

    def _inject_technology_insights(self, results: Dict[str, Any], expert_knowledge: Dict[str, Any]) -> Dict[str, Any]:
        """Inject technology-specific insights"""
        insights = {
            "contextual_interpretations": [
                "Technology metrics should focus on user engagement and retention",
                "Scalability and performance metrics are critical for growth",
                "Innovation metrics indicate competitive positioning"
            ],
            "industry_benchmarks": {
                "user_retention_rate": "40-60%",
                "monthly_active_users_growth": "10-20%",
                "churn_rate": "<5% monthly"
            },
            "best_practices": [
                "Focus on user experience and product-market fit",
                "Implement data-driven development practices",
                "Monitor technical debt and system performance"
            ],
            "expert_opinions": [
                "Technology companies must balance growth with sustainable unit economics",
                "Customer feedback loops are essential for product development"
            ]
        }

        return insights

    def _inject_general_business_insights(self, results: Dict[str, Any], expert_knowledge: Dict[str, Any]) -> Dict[str, Any]:
        """Inject general business insights"""
        insights = {
            "contextual_interpretations": [
                "Business performance should be evaluated holistically across multiple dimensions",
                "Leading indicators are more valuable than lagging indicators for decision making",
                "Market context significantly influences metric interpretation"
            ],
            "industry_benchmarks": {
                "profit_margin": "10-20%",
                "customer_satisfaction": "80%+",
                "employee_retention": "85%+"
            },
            "best_practices": [
                "Establish clear KPI hierarchies and dependencies",
                "Regular benchmarking against industry standards",
                "Implement continuous monitoring and improvement processes"
            ],
            "expert_opinions": [
                "Sustainable business growth requires balanced focus on efficiency and innovation",
                "Data-driven decision making should be complemented by business intuition"
            ]
        }

        return insights

    def _generate_strategic_recommendations(self, results: Dict[str, Any], domain: str, 
                                          strategic_context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate strategic business recommendations"""
        recommendations = {
            "immediate_actions": [],
            "medium_term_initiatives": [],
            "long_term_strategic_goals": [],
            "investment_priorities": []
        }

        # Analyze current performance against strategic goals
        strategic_goals = strategic_context.get("strategic_goals", [])
        current_performance = self._assess_current_performance(results)

        # Generate recommendations based on performance gaps
        if "discovered_kpis" in results:
            kpis = results["discovered_kpis"]
            recommendations["immediate_actions"].extend(
                self._generate_kpi_based_actions(kpis, domain)
            )

        if "trend_analysis" in results:
            trends = results["trend_analysis"]
            recommendations["medium_term_initiatives"].extend(
                self._generate_trend_based_initiatives(trends, domain)
            )

        # Strategic goal alignment
        recommendations["long_term_strategic_goals"] = self._align_with_strategic_goals(
            results, strategic_goals, domain
        )

        # Investment priorities
        recommendations["investment_priorities"] = self._prioritize_investments(
            results, domain, strategic_context
        )

        return recommendations

    def _assess_risks(self, results: Dict[str, Any], domain: str, 
                     strategic_context: Dict[str, Any]) -> Dict[str, Any]:
        """Assess business risks based on analysis results"""
        risk_assessment = {
            "identified_risks": [],
            "risk_severity": {},
            "mitigation_strategies": [],
            "monitoring_requirements": []
        }

        # Performance-based risks
        if "trend_analysis" in results:
            trends = results["trend_analysis"]
            if isinstance(trends, dict):
                trend_direction = trends.get("trend_direction", "stable")
                if trend_direction == "decreasing":
                    risk_assessment["identified_risks"].append({
                        "risk": "Declining performance trend",
                        "category": "operational",
                        "severity": "medium"
                    })

        # Data quality risks
        if "data_quality_assessment" in results:
            quality_data = results["data_quality_assessment"]
            if isinstance(quality_data, dict):
                missing_values = quality_data.get("missing_values", {})
                if isinstance(missing_values, dict) and missing_values.get("total_missing", 0) > 0:
                    risk_assessment["identified_risks"].append({
                        "risk": "Data quality issues affecting analysis reliability",
                        "category": "data",
                        "severity": "low"
                    })

        # Domain-specific risks
        if domain.lower() in ['retail', 'ecommerce']:
            risk_assessment["identified_risks"].extend([
                {"risk": "Seasonal demand fluctuations", "category": "market", "severity": "medium"},
                {"risk": "Competition from digital channels", "category": "strategic", "severity": "high"}
            ])
        elif domain.lower() in ['finance', 'financial']:
            risk_assessment["identified_risks"].extend([
                {"risk": "Regulatory compliance changes", "category": "regulatory", "severity": "high"},
                {"risk": "Market volatility impact", "category": "market", "severity": "medium"}
            ])

        # Generate mitigation strategies
        for risk in risk_assessment["identified_risks"]:
            strategy = self._generate_mitigation_strategy(risk, domain)
            if strategy:
                risk_assessment["mitigation_strategies"].append(strategy)

        return risk_assessment

    def _analyze_opportunities(self, results: Dict[str, Any], domain: str, 
                             strategic_context: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze business opportunities"""
        opportunity_analysis = {
            "growth_opportunities": [],
            "efficiency_improvements": [],
            "innovation_potential": [],
            "market_expansion": []
        }

        # KPI-based opportunities
        if "discovered_kpis" in results:
            kpis = results["discovered_kpis"]
            if isinstance(kpis, dict):
                for kpi_name, kpi_data in kpis.items():
                    if isinstance(kpi_data, dict) and "value" in kpi_data:
                        opportunity = self._identify_kpi_opportunity(kpi_name, kpi_data, domain)
                        if opportunity:
                            opportunity_analysis["growth_opportunities"].append(opportunity)

        # Trend-based opportunities
        if "trend_analysis" in results:
            trends = results["trend_analysis"]
            if isinstance(trends, dict):
                trend_direction = trends.get("trend_direction", "stable")
                if trend_direction == "increasing":
                    opportunity_analysis["growth_opportunities"].append(
                        "Capitalize on positive trend momentum with increased investment"
                    )

        # Domain-specific opportunities
        if domain.lower() in ['retail', 'ecommerce']:
            opportunity_analysis["innovation_potential"].extend([
                "Implement AI-powered personalization",
                "Expand omnichannel capabilities",
                "Optimize supply chain with predictive analytics"
            ])
        elif domain.lower() in ['technology', 'software']:
            opportunity_analysis["innovation_potential"].extend([
                "Leverage machine learning for product enhancement",
                "Expand API ecosystem for platform growth",
                "Implement advanced analytics capabilities"
            ])

        return opportunity_analysis

    def _assess_current_performance(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Assess current performance level"""
        performance = {
            "overall_score": 0.7,  # Default moderate performance
            "strengths": [],
            "weaknesses": []
        }

        # Analyze KPI performance
        if "discovered_kpis" in results:
            kpis = results["discovered_kpis"]
            if isinstance(kpis, dict) and len(kpis) > 0:
                performance["strengths"].append("Strong KPI visibility")
                performance["overall_score"] += 0.1

        # Analyze trend performance
        if "trend_analysis" in results:
            trends = results["trend_analysis"]
            if isinstance(trends, dict):
                trend_direction = trends.get("trend_direction", "stable")
                if trend_direction == "increasing":
                    performance["strengths"].append("Positive growth trends")
                    performance["overall_score"] += 0.1
                elif trend_direction == "decreasing":
                    performance["weaknesses"].append("Declining performance trends")
                    performance["overall_score"] -= 0.1

        return performance

    def _generate_kpi_based_actions(self, kpis: Dict[str, Any], domain: str) -> List[str]:
        """Generate actions based on KPI analysis"""
        actions = []
        
        if isinstance(kpis, dict):
            revenue_kpis = [k for k in kpis.keys() if 'revenue' in k.lower() or 'sales' in k.lower()]
            if revenue_kpis:
                actions.append("Focus on revenue optimization strategies")
            
            customer_kpis = [k for k in kpis.keys() if 'customer' in k.lower()]
            if customer_kpis:
                actions.append("Enhance customer engagement and retention programs")
        
        return actions

    def _generate_trend_based_initiatives(self, trends: Dict[str, Any], domain: str) -> List[str]:
        """Generate initiatives based on trend analysis"""
        initiatives = []
        
        if isinstance(trends, dict):
            growth_rate = trends.get("growth_rate", 0)
            if growth_rate > 10:
                initiatives.append("Scale operations to support continued growth")
            elif growth_rate < -5:
                initiatives.append("Implement turnaround strategy to reverse decline")
        
        return initiatives

    def _align_with_strategic_goals(self, results: Dict[str, Any], goals: List[str], domain: str) -> List[str]:
        """Align recommendations with strategic goals"""
        aligned_goals = []
        
        for goal in goals:
            if "growth" in goal.lower() and "trend_analysis" in results:
                aligned_goals.append(f"Accelerate growth initiatives aligned with {goal}")
            elif "efficiency" in goal.lower() and "discovered_kpis" in results:
                aligned_goals.append(f"Optimize operational efficiency to support {goal}")
        
        return aligned_goals

    def _prioritize_investments(self, results: Dict[str, Any], domain: str, 
                              strategic_context: Dict[str, Any]) -> List[str]:
        """Prioritize investment areas"""
        priorities = []
        
        # Data infrastructure investment
        if "data_quality_assessment" in results:
            priorities.append("Invest in data quality and infrastructure improvements")
        
        # Technology investment
        if domain.lower() in ['technology', 'software', 'digital']:
            priorities.append("Prioritize technology platform and capabilities")
        
        # Domain-specific priorities
        if domain.lower() in ['retail', 'ecommerce']:
            priorities.extend([
                "Customer experience technology",
                "Supply chain optimization systems"
            ])
        
        return priorities

    def _generate_mitigation_strategy(self, risk: Dict[str, Any], domain: str) -> str:
        """Generate mitigation strategy for identified risk"""
        risk_type = risk.get("risk", "").lower()
        
        if "declining" in risk_type or "decreasing" in risk_type:
            return "Implement performance monitoring and early warning systems"
        elif "data quality" in risk_type:
            return "Establish data governance and quality assurance processes"
        elif "competition" in risk_type:
            return "Develop competitive differentiation strategies"
        elif "regulatory" in risk_type:
            return "Enhance compliance monitoring and legal advisory capabilities"
        
        return "Develop comprehensive risk monitoring framework"

    def _identify_kpi_opportunity(self, kpi_name: str, kpi_data: Dict[str, Any], domain: str) -> str:
        """Identify opportunity based on specific KPI"""
        value = kpi_data.get("value", 0)
        kpi_type = kpi_data.get("type", "")
        
        if "revenue" in kpi_name.lower() and isinstance(value, (int, float)) and value > 0:
            return f"Revenue growth opportunity identified in {kpi_name}"
        elif "customer" in kpi_name.lower():
            return f"Customer expansion opportunity in {kpi_name}"
        elif "efficiency" in kpi_type.lower():
            return f"Operational efficiency improvement potential in {kpi_name}"
        
        return None
