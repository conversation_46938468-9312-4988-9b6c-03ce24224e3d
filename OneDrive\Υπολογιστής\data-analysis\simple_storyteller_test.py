"""
Simple test to verify storyteller agent can be imported and executed
"""

def test_storyteller_import():
    """Test if storyteller agent can be imported"""
    try:
        from agents.specialized_business.storyteller_agent.storyteller_agent import StorytellerAgent
        print("✅ StorytellerAgent imported successfully!")
        
        # Test initialization
        agent = StorytellerAgent()
        print("✅ StorytellerAgent initialized successfully!")
        
        # Test a simple execution with minimal data
        test_input = {
            "complete_analysis": {
                "data": {"sales": [100, 200, 300], "region": ["A", "B", "C"]},
                "deepseek_analysis": {"business_context": {"industry": "test"}},
            },
            "data_mining_insights": {"patterns": ["Test pattern"]},
            "expert_opinions": {"business_recommendations": ["Test recommendation"]},
            "business_context": {"industry": "test"},
            "target_audience": "executives"
        }
        
        print("🚀 Testing storyteller agent execution...")
        
        # Execute components directly to avoid input_object issues
        executive_summary = agent._generate_executive_summary(
            test_input["complete_analysis"], 
            test_input["data_mining_insights"], 
            test_input["expert_opinions"], 
            test_input["business_context"]
        )
        
        print("✅ Executive summary generated!")
        print(f"Executive summary keys: {list(executive_summary.keys()) if isinstance(executive_summary, dict) else 'Not a dict'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_storyteller_import()
    if success:
        print("\n🎉 Storyteller agent test completed successfully!")
    else:
        print("\n❌ Storyteller agent test failed!")
