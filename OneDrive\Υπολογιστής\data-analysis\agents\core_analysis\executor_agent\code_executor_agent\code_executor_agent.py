"""
Code Executor Agent for the AI Data Analysis Platform.
This agent executes Python code snippets for data analysis, visualization, and calculations.
"""
import json
import traceback
from typing import Any, Dict, Optional
import io
import sys
from contextlib import redirect_stdout, redirect_stderr

from agentuniverse.agent.agent import Agent


class CodeExecutorAgent(Agent):
    """PEER Pattern - Execution Agent for Code Execution"""

    def __init__(self, **kwargs):
        """Initialize the Code Executor Agent"""
        super().__init__(**kwargs)
        
    def input_keys(self) -> list[str]:
        """Required input keys for this agent"""
        return ["code_snippet", "data_context", "execution_parameters", "safety_constraints"]
    
    def output_keys(self) -> list[str]:
        """Output keys this agent produces"""
        return ["execution_result", "output", "error", "generated_artifacts"]
    
    def parse_input(self, input_object, agent_input):
        """Parse input for the agent"""
        return {
            "code_snippet": agent_input.get("code_snippet", ""),
            "data_context": agent_input.get("data_context", {}),
            "execution_parameters": agent_input.get("execution_parameters", {}),
            "safety_constraints": agent_input.get("safety_constraints", {})
        }
    
    def parse_result(self, agent_result):
        """Parse agent result"""
        return agent_result

    def execute(self, input_object, agent_input):
        """
        Execute Python code safely and capture results.
        """
        code_snippet = agent_input.get("code_snippet", "")
        data_context = agent_input.get("data_context", {})
        execution_parameters = agent_input.get("execution_parameters", {})
        safety_constraints = agent_input.get("safety_constraints", {})

        if not code_snippet:
            return {"error": "No code snippet provided", "success": False}

        # Safety check
        if not self._validate_code_safety(code_snippet):
            return {"error": "Code snippet contains potentially unsafe operations", "success": False}

        try:
            # Prepare execution environment
            exec_globals = {
                '__builtins__': __builtins__,
                'pd': None,  # Will import pandas if available
                'np': None,  # Will import numpy if available
                'plt': None,  # Will import matplotlib if available
                'go': None,  # Will import plotly.graph_objects if available
                'px': None   # Will import plotly.express if available
            }

            # Import common data science libraries if available
            try:
                import pandas as pd
                import numpy as np
                import matplotlib.pyplot as plt
                exec_globals.update({
                    'pd': pd,
                    'np': np,
                    'plt': plt
                })
            except ImportError:
                pass

            # Import visualization libraries if available
            try:
                import plotly.graph_objects as go
                import plotly.express as px
                exec_globals.update({
                    'go': go,
                    'px': px
                })
            except ImportError:
                pass

            # Add data context
            exec_globals.update(data_context)

            # Capture output
            stdout_capture = io.StringIO()
            stderr_capture = io.StringIO()
            
            execution_result = None
            generated_artifacts = {}

            with redirect_stdout(stdout_capture), redirect_stderr(stderr_capture):
                exec_locals = {}
                exec(code_snippet, exec_globals, exec_locals)
                
                # Capture generated variables
                for var_name, var_value in exec_locals.items():
                    if not var_name.startswith('_'):
                        # Check if it's a plot figure
                        if hasattr(var_value, 'show') and hasattr(var_value, 'to_json'):
                            generated_artifacts[f"{var_name}_json"] = var_value.to_json()
                        elif exec_globals.get('pd') and isinstance(var_value, exec_globals['pd'].DataFrame):
                            generated_artifacts[f"{var_name}_data"] = var_value.to_dict()
                        elif isinstance(var_value, (int, float, str, list, dict)):
                            generated_artifacts[var_name] = var_value

                execution_result = exec_locals

            stdout_output = stdout_capture.getvalue()
            stderr_output = stderr_capture.getvalue()

            return {
                "execution_result": execution_result,
                "output": stdout_output,
                "error": stderr_output if stderr_output else None,
                "generated_artifacts": generated_artifacts,
                "success": True
            }

        except Exception as e:
            error_details = {
                "type": type(e).__name__,
                "message": str(e),
                "traceback": traceback.format_exc()
            }
            
            return {
                "execution_result": None,
                "output": "",
                "error": error_details,
                "generated_artifacts": {},
                "success": False
            }

    def _validate_code_safety(self, code: str) -> bool:
        """
        Basic code safety validation.
        This is a simple check - for production use, consider more robust sandboxing.
        """
        dangerous_keywords = [
            'import os', 'import sys', 'import subprocess',
            'open(', 'file(', 'input(', '__import__',
            'exec(', 'eval(', 'compile(',
            'globals(', 'locals(', 'vars(',
            'dir(', 'delattr', 'setattr',
            'rm ', 'del ', 'remove'
        ]
        
        code_lower = code.lower()
        for keyword in dangerous_keywords:
            if keyword in code_lower:
                return False
        return True

