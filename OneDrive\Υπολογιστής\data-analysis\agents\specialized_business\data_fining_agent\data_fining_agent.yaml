# AgentUniverse Configuration for DataFiningAgent
name: "DataFiningAgent"
description: "Performs deep data exploration and pattern discovery as part of the DOE Data-fining phase"
agent_type: "react"

class_name: "data_fining_agent.DataFiningAgent"

llm:
  type: "deepseek_llm"
  model: "deepseek-v3" # Most capable model for deep pattern discovery
  # llm_config_name: "deepseek_mining_config"

prompt:
  version: "data_fining_v1"
  # Template for deep data mining prompt
  # prompt_template: |
  #   You are an expert data mining specialist with deep knowledge of advanced analytics and pattern discovery.
  #   
  #   PEER Analysis Results: {peer_results}
  #   Raw Data Summary: {raw_data_summary}
  #   Business Domain: {business_domain}
  #   Advanced Analytics Goals: {advanced_analytics_goals}
  #   Existing Insights: {existing_insights}
  #   
  #   Your task is to perform deep data mining to discover hidden patterns and correlations that were not captured in the initial PEER analysis.
  #   
  #   Focus on discovering:
  #   1. Hidden correlations between seemingly unrelated variables
  #   2. Temporal patterns and cycles not yet identified
  #   3. Segmentation opportunities within the data
  #   4. Anomalies that might represent opportunities or risks
  #   5. Cross-functional relationships between business metrics
  #   6. Market signals embedded in the data
  #   7. Predictive indicators for future performance
  #   8. Optimization opportunities for business processes
  #   
  #   Use advanced analytical techniques conceptually:
  #   - Clustering analysis for customer/product segmentation
  #   - Association rule mining for relationship discovery
  #   - Time series decomposition for trend analysis
  #   - Outlier detection for anomaly identification
  #   - Feature importance analysis for key driver identification
  #   
  #   Output in JSON format:
  #   {
  #     "hidden_patterns": [
  #       {
  #         "pattern_type": "correlation|temporal|segmentation|anomaly",
  #         "description": "...",
  #         "strength": "high|medium|low",
  #         "business_significance": "...",
  #         "supporting_evidence": [...],
  #         "actionable_insights": [...]
  #       }
  #     ],
  #     "deep_correlations": {
  #       "unexpected_relationships": [...],
  #       "causal_chains": [...],
  #       "interaction_effects": [...]
  #     },
  #     "advanced_insights": {
  #       "customer_segmentation": {...},
  #       "product_optimization": {...},
  #       "process_improvements": {...},
  #       "market_opportunities": {...}
  #     },
  #     "predictive_indicators": [
  #       {
  #         "indicator": "...",
  #         "predictive_power": "high|medium|low",
  #         "lead_time": "...",
  #         "business_impact": "..."
  #       }
  #     ],
  #     "data_mining_recommendations": {
  #       "additional_data_needed": [...],
  #       "advanced_techniques_to_apply": [...],
  #       "validation_methods": [...]
  #     }
  #   }

output_parser:
  class_name: "data_fining_agent.DataFiningAgentOutputParser"

# Input and output examples
# input_keys: ["peer_results", "raw_data_summary", "business_domain", "advanced_analytics_goals"]
# output_keys: ["hidden_patterns", "deep_correlations", "advanced_insights", "predictive_indicators"]
