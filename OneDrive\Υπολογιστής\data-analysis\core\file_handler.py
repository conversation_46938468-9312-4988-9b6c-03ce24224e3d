"""
File Upload Handler for AI Data Analysis Platform
Clean working version for CEO demonstration
"""

import pandas as pd
import json
import logging
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
import chardet
from datetime import datetime
import mimetypes
import os

logger = logging.getLogger(__name__)


class FileUploadHandler:
    """
    Handles file uploads and data preparation for the AI analysis pipeline
    
    Supports:
    - CSV files
    - Excel files (.xlsx, .xls)
    - JSON files
    """
    
    def __init__(self):
        self.supported_formats = {
            'csv': self._process_csv,
            'excel': self._process_excel,
            'json': self._process_json
        }
        
    def process_uploaded_file(self, file_path: str = None, file_content: bytes = None) -> Dict:
        """
        Main method to process uploaded files
        
        Args:
            file_path: Path to the uploaded file
            file_content: Raw file content (bytes)
            
        Returns:
            Processed data ready for DeepSeek analysis
        """
        try:
            # Validate inputs
            if not file_path and not file_content:
                return {
                    "error": "Either file_path or file_content must be provided",
                    "processing_status": "failed"
                }
            
            # Use default filename if only content is provided
            if not file_path:
                file_path = "uploaded_file.csv"  # Default assumption
            
            # Determine file type
            file_info = self._analyze_file(file_path, file_content)
            
            # Get file format
            file_format = file_info.get("format", "unknown")
            
            if file_format in self.supported_formats:
                # Process with appropriate handler
                processed_data = self.supported_formats[file_format](file_path, file_content)
                
                # Combine results
                result = {
                    "uploaded_file_data": processed_data,
                    "file_metadata": file_info,
                    "processing_status": "success",
                    "processing_timestamp": datetime.now().isoformat()
                }
                
                logger.info(f"✅ File processed successfully: {file_path}")
                return result
                
            else:
                return {
                    "error": f"Unsupported file format: {file_format}",
                    "processing_status": "failed",
                    "supported_formats": list(self.supported_formats.keys())
                }
                
        except Exception as e:
            logger.error(f"❌ File processing failed: {e}")
            return {
                "error": str(e),
                "processing_status": "failed",
                "file_metadata": self._get_basic_file_info(file_path or "unknown")
            }
    
    def _analyze_file(self, file_path: str, file_content: bytes = None) -> Dict:
        """Analyze file and determine its characteristics"""
        path_obj = Path(file_path)
        
        # Basic file info
        file_info = {
            "filename": path_obj.name,
            "file_extension": path_obj.suffix.lower(),
            "file_size": len(file_content) if file_content else (path_obj.stat().st_size if path_obj.exists() else 0)
        }
        
        # Determine format based on extension
        extension = file_info["file_extension"]
        if extension == '.csv':
            file_info["format"] = "csv"
            file_info["mime_type"] = "text/csv"
        elif extension in ['.xlsx', '.xls']:
            file_info["format"] = "excel"
            file_info["mime_type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        elif extension == '.json':
            file_info["format"] = "json"
            file_info["mime_type"] = "application/json"
        else:
            file_info["format"] = "unknown"
            file_info["mime_type"] = "unknown"
        
        # Detect encoding for text files
        if file_content and file_info["format"] in ["csv", "json"]:
            try:
                encoding_result = chardet.detect(file_content)
                file_info["encoding"] = encoding_result.get("encoding", "utf-8")
                file_info["encoding_confidence"] = encoding_result.get("confidence", 0)
            except:
                file_info["encoding"] = "utf-8"
                file_info["encoding_confidence"] = 0
        
        return file_info
    
    def _process_csv(self, file_path: str, file_content: bytes = None) -> Dict:
        """Process CSV files"""
        try:
            if file_content:
                # Detect encoding
                encoding = chardet.detect(file_content).get("encoding", "utf-8")
                # Read from bytes
                import io
                df = pd.read_csv(io.BytesIO(file_content), encoding=encoding)
            else:
                df = pd.read_csv(file_path)
            
            # Convert to structured format
            processed_data = {
                "data": df,
                "records": df.to_dict('records'),
                "columns": list(df.columns),
                "row_count": len(df),
                "column_count": len(df.columns),
                "data_types": self._analyze_column_types(df),
                "sample_data": df.head(5).to_dict('records'),
                "data_quality": self._assess_data_quality(df)
            }
            
            return processed_data
            
        except Exception as e:
            logger.error(f"CSV processing error: {e}")
            raise
    
    def _process_excel(self, file_path: str, file_content: bytes = None) -> Dict:
        """Process Excel files"""
        try:
            if file_content:
                import io
                excel_data = pd.read_excel(io.BytesIO(file_content), sheet_name=None)
            else:
                excel_data = pd.read_excel(file_path, sheet_name=None)
            
            # Handle multiple sheets
            if len(excel_data) == 1:
                # Single sheet - treat like CSV
                sheet_name = list(excel_data.keys())[0]
                df = excel_data[sheet_name]
                
                processed_data = {
                    "data": df,
                    "records": df.to_dict('records'),
                    "columns": list(df.columns),
                    "row_count": len(df),
                    "column_count": len(df.columns),
                    "data_types": self._analyze_column_types(df),
                    "sample_data": df.head(5).to_dict('records'),
                    "data_quality": self._assess_data_quality(df),
                    "sheet_info": {
                        "active_sheet": sheet_name,
                        "total_sheets": len(excel_data),
                        "sheet_names": list(excel_data.keys())
                    }
                }
            else:
                # Multiple sheets - combine data
                all_data = []
                sheet_info = []
                
                for sheet_name, sheet_df in excel_data.items():
                    all_data.extend(sheet_df.to_dict('records'))
                    sheet_info.append({
                        "name": sheet_name,
                        "rows": len(sheet_df),
                        "columns": list(sheet_df.columns)
                    })
                
                # Use first sheet as primary structure
                primary_sheet = list(excel_data.values())[0]
                
                processed_data = {
                    "data": primary_sheet,
                    "records": all_data,
                    "columns": list(primary_sheet.columns),
                    "row_count": len(all_data),
                    "column_count": len(primary_sheet.columns),
                    "data_types": self._analyze_column_types(primary_sheet),
                    "sample_data": all_data[:5],
                    "data_quality": self._assess_data_quality(primary_sheet),
                    "sheet_info": {
                        "active_sheet": list(excel_data.keys())[0],
                        "total_sheets": len(excel_data),
                        "sheet_names": list(excel_data.keys()),
                        "sheets_detail": sheet_info
                    }
                }
            
            return processed_data
            
        except Exception as e:
            logger.error(f"Excel processing error: {e}")
            raise
    
    def _process_json(self, file_path: str, file_content: bytes = None) -> Dict:
        """Process JSON files"""
        try:
            if file_content:
                # Detect encoding and decode
                encoding = chardet.detect(file_content).get("encoding", "utf-8")
                json_str = file_content.decode(encoding)
            else:
                with open(file_path, 'r', encoding='utf-8') as f:
                    json_str = f.read()
            
            # Parse JSON
            data = json.loads(json_str)
            
            # Convert to DataFrame if possible
            if isinstance(data, list) and len(data) > 0:
                df = pd.DataFrame(data)
            elif isinstance(data, dict):
                if all(isinstance(v, list) and len(v) == len(list(data.values())[0]) for v in data.values()):
                    # Dictionary with equal-length arrays
                    df = pd.DataFrame(data)
                else:
                    # Single record or nested structure
                    df = pd.DataFrame([data])
            else:
                # Fallback: create single-column DataFrame
                df = pd.DataFrame({"data": [data]})
            
            processed_data = {
                "data": df,
                "records": df.to_dict('records'),
                "columns": list(df.columns),
                "row_count": len(df),
                "column_count": len(df.columns),
                "data_types": self._analyze_column_types(df),
                "sample_data": df.head(5).to_dict('records'),
                "data_quality": self._assess_data_quality(df),
                "json_structure": type(data).__name__
            }
            
            return processed_data
            
        except Exception as e:
            logger.error(f"JSON processing error: {e}")
            raise
    
    def _analyze_column_types(self, df: pd.DataFrame) -> Dict:
        """Analyze data types of DataFrame columns"""
        type_analysis = {}
        
        for column in df.columns:
            col_data = df[column]
            
            type_analysis[column] = {
                "pandas_dtype": str(col_data.dtype),
                "null_count": col_data.isnull().sum(),
                "unique_count": col_data.nunique(),
                "inferred_type": self._infer_business_type(col_data)
            }
        
        return type_analysis
    
    def _infer_business_type(self, series: pd.Series) -> str:
        """Infer business-relevant data type"""
        # Check for common business data patterns
        sample_values = series.dropna().astype(str).str.lower()
        column_name = str(series.name).lower() if series.name else ""

        if series.dtype in ['int64', 'float64']:
            if any(word in column_name for word in ['price', 'cost', 'revenue', 'amount', 'value']):
                return "monetary"
            elif any(word in column_name for word in ['count', 'quantity', 'number', 'qty']):
                return "quantity"
            else:
                return "numeric"
        elif pd.api.types.is_datetime64_any_dtype(series):
            return "datetime"
        elif sample_values.str.contains(r'\b(?:email|@)\b', regex=True).any():
            return "email"
        elif sample_values.str.contains(r'\b(?:phone|tel)\b', regex=True).any():
            return "phone"
        else:
            return "categorical"
    
    def _assess_data_quality(self, df: pd.DataFrame) -> Dict:
        """Assess data quality metrics"""
        total_cells = df.shape[0] * df.shape[1]
        null_cells = df.isnull().sum().sum()
        
        return {
            "completeness": 1 - (null_cells / total_cells) if total_cells > 0 else 1,
            "total_records": len(df),
            "null_values": int(null_cells),
            "duplicate_rows": df.duplicated().sum(),
            "data_quality_score": min(1.0, max(0.0, 1 - (null_cells / total_cells))),
            "quality_issues": self._identify_quality_issues(df)
        }
    
    def _identify_quality_issues(self, df: pd.DataFrame) -> List[str]:
        """Identify potential data quality issues"""
        issues = []
        
        # Check for high null percentage
        for column in df.columns:
            null_pct = df[column].isnull().sum() / len(df)
            if null_pct > 0.5:
                issues.append(f"High missing data in column '{column}' ({null_pct:.1%})")
        
        # Check for duplicate rows
        duplicates = df.duplicated().sum()
        if duplicates > 0:
            issues.append(f"{duplicates} duplicate rows found")
        
        # Check for constant columns
        for column in df.columns:
            if df[column].nunique() == 1:
                issues.append(f"Column '{column}' has constant values")
        
        return issues
    
    def _get_basic_file_info(self, file_path: str) -> Dict:
        """Get basic file information for error cases"""
        path_obj = Path(file_path)
        return {
            "filename": path_obj.name,
            "file_extension": path_obj.suffix.lower(),
            "file_size": path_obj.stat().st_size if path_obj.exists() else 0,
            "exists": path_obj.exists()
        }
