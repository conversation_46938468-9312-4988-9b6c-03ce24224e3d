# AgentUniverse Configuration for ReviewerAgent
name: "ReviewerAgent"
description: "Provides quality assurance and insight validation as part of the PEER Review phase"
agent_type: "react"

class_name: "reviewer_agent.ReviewerAgent"

llm:
  type: "deepseek_llm"
  model: "deepseek-r1" # Reasoning model for analytical review
  # llm_config_name: "deepseek_review_config"

prompt:
  version: "reviewer_quality_v1"
  # Template for quality review prompt
  # prompt_template: |
  #   You are an expert data analysis quality assurance specialist and business intelligence reviewer.
  #   
  #   Complete Analysis: {complete_analysis}
  #   Original Plan: {original_plan}
  #   Execution Results: {execution_results}
  #   Visualization Results: {visualization_results}
  #   Business Context: {business_context}
  #   Data Quality Metrics: {data_quality_metrics}
  #   
  #   Your task is to conduct a comprehensive quality review of the entire analysis process.
  #   
  #   Evaluate the following aspects:
  #   1. Plan Execution Fidelity: Did the execution match the original plan?
  #   2. Data Quality Assessment: Are the data quality issues properly addressed?
  #   3. Statistical Validity: Are the statistical methods and conclusions sound?
  #   4. Business Relevance: Do the insights align with business context and goals?
  #   5. Visualization Effectiveness: Are the charts and dashboards clear and actionable?
  #   6. KPI Appropriateness: Are the selected KPIs relevant and well-calculated?
  #   7. Forecast Reliability: Are the predictions reasonable and well-justified?
  #   8. Insight Actionability: Can business owners act on these recommendations?
  #   
  #   Provide specific improvement recommendations for any issues found.
  #   Suggest additional analysis that might strengthen the conclusions.
  #   Identify potential biases or limitations in the current analysis.
  #   
  #   Output in JSON format:
  #   {
  #     "quality_scores": {
  #       "plan_execution_fidelity": 85,
  #       "data_quality": 90,
  #       "statistical_validity": 88,
  #       "business_relevance": 92,
  #       "visualization_effectiveness": 87,
  #       "kpi_appropriateness": 90,
  #       "forecast_reliability": 82,
  #       "insight_actionability": 89,
  #       "overall_quality_score": 88
  #     },
  #     "issues_found": [
  #       {
  #         "category": "...",
  #         "severity": "high|medium|low",
  #         "description": "...",
  #         "impact": "...",
  #         "affected_components": [...]
  #       }
  #     ],
  #     "improvement_recommendations": [
  #       {
  #         "priority": "high|medium|low",
  #         "recommendation": "...",
  #         "rationale": "...",
  #         "implementation_steps": [...],
  #         "expected_impact": "..."
  #       }
  #     ],
  #     "additional_analysis_suggestions": [...],
  #     "limitations_and_caveats": [...],
  #     "confidence_assessment": {
  #       "overall_confidence": "high|medium|low",
  #       "uncertainty_factors": [...],
  #       "validation_recommendations": [...]
  #     },
  #     "iterative_improvement_plan": {
  #       "should_iterate": true|false,
  #       "iteration_focus_areas": [...],
  #       "expected_improvements": [...]
  #     }
  #   }

output_parser:
  class_name: "reviewer_agent.ReviewerAgentOutputParser"

# Input and output examples
# input_keys: ["complete_analysis", "original_plan", "execution_results", "visualization_results", "business_context"]
# output_keys: ["quality_scores", "issues_found", "improvement_recommendations", "confidence_assessment"]
