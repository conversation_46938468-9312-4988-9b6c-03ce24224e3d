#!/usr/bin/env python3
"""
Debug script to trace exactly what happens to plots during serialization
"""

import json
import pandas as pd
from main_platform import AIDataAnalysisPlatform
from web_interface import make_json_serializable

async def debug_plot_loss():
    """Debug where plots are getting lost"""
    print("🔍 Debugging plot loss during serialization...")
    
    # Create simple test data
    test_data = pd.DataFrame({
        'Sales': [100, 150, 200, 120, 180],
        'Region': ['North', 'South', 'East', 'West', 'Central'],
        'Revenue': [1000, 1500, 2000, 1200, 1800]
    })
    
    # Save to CSV for testing
    test_data.to_csv('debug_test.csv', index=False)
    
    try:
        # Initialize platform
        platform = AIDataAnalysisPlatform()
        
        # Read test file
        with open('debug_test.csv', 'rb') as f:
            content = f.read()
        
        print("🚀 Running platform analysis...")
        # Analyze with platform
        raw_result = await platform.analyze_business_data('debug_test.csv', content)
        
        # Check the raw result structure
        print("\n📊 RAW RESULT INSPECTION:")
        if 'agent_orchestration' in raw_result:
            orch = raw_result['agent_orchestration']
            if 'visualization_results' in orch:
                viz = orch['visualization_results']
                print(f"🎨 Raw viz total_plots: {viz.get('total_plots', 'N/A')}")
                raw_plots = viz.get('plots', {})
                print(f"🎨 Raw plots type: {type(raw_plots)}")
                print(f"🎨 Raw plots count: {len(raw_plots) if raw_plots else 0}")
                
                if raw_plots:
                    first_plot_name = list(raw_plots.keys())[0]
                    first_plot = raw_plots[first_plot_name]
                    print(f"🎨 First plot '{first_plot_name}':")
                    print(f"   Type: {type(first_plot)}")
                    print(f"   Length: {len(str(first_plot)) if first_plot else 0}")
                    print(f"   Is HTML: {'<' in str(first_plot) if first_plot else False}")
        
        # Now test serialization
        print("\n🧪 TESTING SERIALIZATION:")
        try:
            serialized = make_json_serializable(raw_result)
            print("✅ Serialization completed")
            
            # Check if visualization data survived
            if 'agent_orchestration' in serialized:
                s_orch = serialized['agent_orchestration']
                if 'visualization_results' in s_orch:
                    s_viz = s_orch['visualization_results']
                    print(f"🎨 Serialized viz total_plots: {s_viz.get('total_plots', 'N/A')}")
                    s_plots = s_viz.get('plots', {})
                    print(f"🎨 Serialized plots type: {type(s_plots)}")
                    print(f"🎨 Serialized plots count: {len(s_plots) if s_plots else 0}")
                    
                    if s_plots and raw_plots:
                        first_plot_name = list(raw_plots.keys())[0]
                        if first_plot_name in s_plots:
                            s_first_plot = s_plots[first_plot_name]
                            print(f"🎨 Serialized first plot '{first_plot_name}':")
                            print(f"   Type: {type(s_first_plot)}")
                            print(f"   Length: {len(str(s_first_plot)) if s_first_plot else 0}")
                            print(f"   Is HTML: {'<' in str(s_first_plot) if s_first_plot else False}")
                        else:
                            print(f"❌ First plot '{first_plot_name}' missing after serialization")
                    elif not s_plots:
                        print("❌ All plots lost during serialization")
                else:
                    print("❌ visualization_results missing after serialization")
            else:
                print("❌ agent_orchestration missing after serialization")
                
        except Exception as e:
            print(f"❌ Serialization failed: {e}")
            import traceback
            traceback.print_exc()
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        import os
        if os.path.exists('debug_test.csv'):
            os.remove('debug_test.csv')

if __name__ == "__main__":
    import asyncio
    asyncio.run(debug_plot_loss())
