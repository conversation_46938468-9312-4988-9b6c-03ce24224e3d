# AgentUniverse Configuration for OpinionInjectAgent
name: "OpinionInjectAgent"
description: "Integrates business domain expertise and industry best practices as part of the DOE Opinion-inject phase"
agent_type: "react"

class_name: "opinion_inject_agent.OpinionInjectAgent"

llm:
  type: "deepseek_llm"
  model: "deepseek-v3" # Most capable model for expert knowledge integration
  # llm_config_name: "deepseek_expert_config"

prompt:
  version: "opinion_inject_v1"
  # Template for expert opinion integration prompt
  # prompt_template: |
  #   You are a senior business consultant with deep expertise across multiple industries and extensive knowledge of business best practices.
  #   
  #   Analysis Results: {analysis_results}
  #   Business Industry: {business_industry}
  #   Company Size: {company_size}
  #   Market Conditions: {market_conditions}
  #   Competitive Landscape: {competitive_landscape}
  #   Regulatory Environment: {regulatory_environment}
  #   
  #   Your task is to inject expert opinions and industry knowledge to enhance the analysis with professional insights.
  #   
  #   Provide expert perspective on:
  #   1. Industry benchmarks and performance standards
  #   2. Best practices specific to this business type
  #   3. Strategic recommendations based on industry experience
  #   4. Risk assessment and mitigation strategies
  #   5. Competitive positioning insights
  #   6. Market opportunity identification
  #   7. Operational excellence recommendations
  #   8. Regulatory compliance considerations
  #   9. Technology adoption strategies
  #   10. Future industry trends and implications
  #   
  #   Consider the specific context:
  #   - Company size constraints and opportunities
  #   - Industry lifecycle stage
  #   - Competitive dynamics
  #   - Economic environment impact
  #   - Regulatory landscape
  #   - Technology disruption factors
  #   
  #   Output in JSON format:
  #   {
  #     "expert_recommendations": [
  #       {
  #         "category": "strategic|operational|financial|marketing|technology",
  #         "recommendation": "...",
  #         "priority": "high|medium|low",
  #         "rationale": "...",
  #         "implementation_timeline": "...",
  #         "expected_impact": "...",
  #         "success_metrics": [...],
  #         "potential_risks": [...]
  #       }
  #     ],
  #     "industry_insights": {
  #       "benchmarks": {
  #         "kpi_targets": {...},
  #         "performance_standards": {...},
  #         "industry_averages": {...}
  #       },
  #       "best_practices": [...],
  #       "common_pitfalls": [...],
  #       "success_factors": [...]
  #     },
  #     "strategic_advice": {
  #       "competitive_positioning": "...",
  #       "market_opportunities": [...],
  #       "differentiation_strategies": [...],
  #       "growth_pathways": [...]
  #     },
  #     "risk_assessment": {
  #       "industry_risks": [...],
  #       "market_risks": [...],
  #       "operational_risks": [...],
  #       "mitigation_strategies": [...]
  #     },
  #     "future_outlook": {
  #       "industry_trends": [...],
  #       "technology_impacts": [...],
  #       "regulatory_changes": [...],
  #       "preparation_recommendations": [...]
  #     },
  #     "contextual_considerations": {
  #       "company_size_factors": "...",
  #       "market_timing": "...",
  #       "resource_constraints": "...",
  #       "capability_gaps": [...]
  #     }
  #   }

output_parser:
  class_name: "opinion_inject_agent.OpinionInjectAgentOutputParser"

# Input and output examples
# input_keys: ["analysis_results", "business_industry", "company_size", "market_conditions", "competitive_landscape"]
# output_keys: ["expert_recommendations", "industry_insights", "strategic_advice", "risk_assessment", "future_outlook"]
