# AgentUniverse Configuration for ExpresserAgent
name: "ExpresserAgent"
description: "Creates compelling business visualizations and dashboards as part of the PEER Express phase"
agent_type: "react"

class_name: "expresser_agent.ExpresserAgent"

llm:
  type: "deepseek_llm"
  model: "deepseek-v3" # Most capable model for creative visualization
  # llm_config_name: "deepseek_visualization_config"

prompt:
  version: "expresser_visualization_v1"
  # Template for visualization creation prompt
  # prompt_template: |
  #   You are an expert data visualization designer and business dashboard architect.
  #   
  #   Analysis Results: {analysis_results}
  #   Business Context: {business_context}
  #   Target Audience: {target_audience}
  #   KPI Results: {kpi_results}
  #   Forecast Results: {forecast_results}
  #   Statistical Insights: {statistical_insights}
  #   
  #   Your task is to design compelling visualizations and dashboards that tell the business story.
  #   
  #   Create a comprehensive visualization strategy including:
  #   1. Executive summary dashboard with key metrics
  #   2. Detailed KPI tracking charts
  #   3. Trend analysis and forecasting visualizations
  #   4. Interactive elements for exploration
  #   5. Business storytelling through visual narrative
  #   6. Color schemes and branding appropriate for business context
  #   7. Mobile-responsive design considerations
  #   
  #   Generate Python code using Plotly for interactive visualizations.
  #   Include proper chart types, layouts, and styling.
  #   
  #   Output in JSON format:
  #   {
  #     "dashboard_design": {
  #       "layout": "grid|tabs|sidebar",
  #       "theme": "professional|modern|minimal",
  #       "color_scheme": {...},
  #       "sections": [...]
  #     },
  #     "chart_specifications": [
  #       {
  #         "chart_id": "...",
  #         "chart_type": "line|bar|scatter|heatmap|...",
  #         "title": "...",
  #         "description": "...",
  #         "data_source": "...",
  #         "interactive_features": [...],
  #         "business_insight": "..."
  #       }
  #     ],
  #     "visualization_code": {
  #       "plotly_charts": "# Python code for Plotly charts...",
  #       "dashboard_assembly": "# Code to combine charts into dashboard...",
  #       "styling": "# CSS/styling code..."
  #     },
  #     "storytelling_elements": {
  #       "narrative_flow": "...",
  #       "key_insights_callouts": [...],
  #       "action_recommendations": [...],
  #       "visual_hierarchy": "..."
  #     },
  #     "export_options": {
  #       "pdf_report": "# Code to generate PDF...",
  #       "interactive_html": "# Code for HTML dashboard...",
  #       "presentation_slides": "# Code for slide generation..."
  #     }
  #   }

output_parser:
  class_name: "expresser_agent.ExpresserAgentOutputParser"

# Input and output examples
# input_keys: ["analysis_results", "business_context", "target_audience", "kpi_results", "forecast_results"]
# output_keys: ["dashboard_design", "chart_specifications", "visualization_code", "storytelling_elements"]
