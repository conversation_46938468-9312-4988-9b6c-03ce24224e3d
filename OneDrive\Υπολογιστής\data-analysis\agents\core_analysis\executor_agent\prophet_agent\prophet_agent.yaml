# AgentUniverse Configuration for ProphetAgent
name: "ProphetAgent"
description: "Creates time series forecasting models and predictions using Prophet library with DeepSeek intelligence"
agent_type: "react"

class_name: "prophet_agent.ProphetAgent"

llm:
  type: "deepseek_llm"
  model: "deepseek-r1" # Reasoning model for forecasting strategy
  # llm_config_name: "deepseek_forecasting_config"

prompt:
  version: "prophet_forecasting_v1"
  # Template for Prophet forecasting prompt
  # prompt_template: |
  #   You are an expert time series analyst specializing in business forecasting using Facebook Prophet.
  #   
  #   Target Metrics: {target_metrics}
  #   Data Summary: {data_summary}
  #   Business Context: {business_context}
  #   Forecast Horizon: {forecast_horizon} periods
  #   Historical Patterns: {historical_patterns}
  #   
  #   Your task is to create an intelligent forecasting strategy using <PERSON> library.
  #   
  #   Analyze the data and determine:
  #   1. Best Prophet parameters for this business context
  #   2. Seasonality patterns to capture (yearly, weekly, daily)
  #   3. Holiday effects if relevant to the business
  #   4. Trend changepoints and growth patterns
  #   5. Uncertainty intervals and confidence levels
  #   6. Business interpretation of forecast results
  #   
  #   Generate Python code using Prophet library that can be executed.
  #   Include proper data preprocessing, model fitting, and visualization.
  #   
  #   Output in JSON format:
  #   {
  #     "prophet_parameters": {
  #       "seasonality_mode": "additive|multiplicative",
  #       "yearly_seasonality": true|false|"auto",
  #       "weekly_seasonality": true|false|"auto", 
  #       "daily_seasonality": true|false|"auto",
  #       "changepoint_prior_scale": 0.05,
  #       "holidays_prior_scale": 10.0,
  #       "interval_width": 0.8
  #     },
  #     "forecasting_code": "# Python code using Prophet...",
  #     "data_preprocessing": "# Code to prepare data for Prophet...",
  #     "visualization_code": "# Code to create forecast plots...",
  #     "forecast_interpretation": {
  #       "trend_analysis": "...",
  #       "seasonality_insights": "...", 
  #       "business_implications": "...",
  #       "confidence_assessment": "...",
  #       "recommended_actions": [...]
  #     },
  #     "model_validation": {
  #       "cross_validation_strategy": "...",
  #       "performance_metrics": [...]
  #     }
  #   }

output_parser:
  class_name: "prophet_agent.ProphetAgentOutputParser"

# Input and output examples  
# input_keys: ["target_metrics", "data_summary", "business_context", "forecast_horizon", "historical_patterns"]
# output_keys: ["prophet_parameters", "forecasting_code", "forecast_interpretation", "model_validation"]
