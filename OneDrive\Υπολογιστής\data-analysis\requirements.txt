# AI Data Analysis Platform - Requirements
# Core AgentUniverse Framework and LLM Dependencies
agentuniverse>=0.0.9
openai>=1.0.0

# DeepSeek API Integration (for custom LLM integration)
requests>=2.31.0
httpx>=0.25.0

# Data Analysis and Processing
pandas>=2.0.0
numpy>=1.24.0
scipy>=1.11.0
scikit-learn>=1.3.0

# Data Visualization
plotly>=5.15.0
matplotlib>=3.7.0
seaborn>=0.12.0

# Time Series Analysis and Forecasting (for ProphetAgent)
prophet>=1.1.4
statsmodels>=0.14.0

# Excel and File Processing
openpyxl>=3.1.0
xlsxwriter>=3.1.0
python-docx>=0.8.11

# Database Connectivity (optional)
sqlalchemy>=2.0.0
psycopg2-binary>=2.9.0
pymongo>=4.5.0

# Web Framework (for API integration)
fastapi>=0.103.0
uvicorn>=0.23.0
flask>=2.3.0

# Configuration and Environment Management
pydantic>=2.0.0
python-dotenv>=1.0.0
toml>=0.10.2

# Async Processing and Performance
asyncio-throttle>=1.0.2
# concurrent-futures is built-in for Python 3.2+

# Logging and Monitoring
loguru>=0.7.0
structlog>=23.1.0

# Testing and Development
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.7.0
flake8>=6.0.0
mypy>=1.5.0

# Memory and Caching
redis>=4.6.0
diskcache>=5.6.0

# Text Processing and NLP (optional)
nltk>=3.8.1
spacy>=3.7.0
transformers>=4.33.0

# Jupyter Notebook Support (for development)
jupyter>=1.0.0
ipython>=8.14.0

# Security and Authentication
cryptography>=41.0.0
pyjwt>=2.8.0

# File Type Support
pillow>=10.0.0
python-magic>=0.4.27

# Additional Utilities
click>=8.1.0
tqdm>=4.66.0
python-dateutil>=2.8.2
pytz>=2023.3

# Optional: Streamlit for UI (if needed)
streamlit>=1.26.0

# Optional: DSPy for additional LLM capabilities
dspy-ai>=2.4.0
