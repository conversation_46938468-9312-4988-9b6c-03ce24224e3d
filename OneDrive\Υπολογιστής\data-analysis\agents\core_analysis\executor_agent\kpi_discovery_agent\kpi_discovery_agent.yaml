# AgentUniverse Configuration for KPIDiscoveryAgent
name: "KPIDiscoveryAgent"
description: "Discovers and prioritizes relevant KPIs based on business context and data structure"
agent_type: "react"

class_name: "kpi_discovery_agent.KPIDiscoveryAgent"

llm:
  type: "deepseek_llm"
  model: "deepseek-v3" # Most capable model for KPI discovery
  # llm_config_name: "deepseek_v3_config"

prompt:
  version: "kpi_discovery_v1"
  # Template for KPI discovery prompt
  # prompt_template: |
  #   You are an expert business analyst specializing in KPI identification and business metrics.
  #   
  #   Business Type: {business_type}
  #   Suggested KPIs from Pre-Analysis: {suggested_kpis}
  #   Data Structure: {data_structure}
  #   Available Columns: {available_columns}
  #   Analysis Goals: {analysis_goals}
  #   
  #   Your task is to discover and prioritize the most relevant KPIs for this business.
  #   Consider:
  #   1. Industry-standard KPIs for this business type
  #   2. KPIs that can be calculated from available data columns
  #   3. Leading vs lagging indicators
  #   4. Financial, operational, and strategic metrics
  #   5. Actionable insights these KPIs can provide
  #   
  #   Output your KPI discovery in JSON format:
  #   {
  #     "discovered_kpis": [
  #       {
  #         "kpi_name": "...",
  #         "description": "...",
  #         "calculation_method": "...",
  #         "required_columns": [...],
  #         "business_importance": "high|medium|low",
  #         "kpi_type": "financial|operational|strategic|customer",
  #         "frequency": "daily|weekly|monthly|quarterly",
  #         "benchmark_ranges": {...}
  #       }
  #     ],
  #     "priority_kpis": [...], 
  #     "missing_data_for_kpis": [...],
  #     "industry_insights": "...",
  #     "recommended_dashboards": [...]
  #   }

output_parser:
  class_name: "kpi_discovery_agent.KPIDiscoveryAgentOutputParser"

# Input and output examples
# input_keys: ["business_type", "suggested_kpis", "data_structure", "available_columns", "analysis_goals"]
# output_keys: ["discovered_kpis", "priority_kpis", "missing_data_for_kpis", "industry_insights"]
