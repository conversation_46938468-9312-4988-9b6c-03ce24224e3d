"""
Test script to verify minimal visualization generation
"""

import asyncio
import json
import logging
import pandas as pd
from core.enhanced_orchestrator_temp import EnhancedAgentUniverseOrchestrator

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_minimal_visualization():
    """Test the minimal visualization approach"""
    
    # Sample test data
    test_data = {
        "filename": "test_business_data.csv",
        "data": {
            "sales": [1000, 1200, 1100, 1300, 1250, 1400, 1350],
            "region": ["North", "South", "East", "West", "Central", "North", "South"],
            "month": ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul"],
            "profit": [200, 240, 220, 260, 250, 280, 270],
            "customers": [50, 60, 55, 65, 62, 70, 68]
        }
    }
    
    # Initialize orchestrator
    orchestrator = EnhancedAgentUniverseOrchestrator()
    
    try:
        logger.info("🚀 Testing minimal visualization generation...")
        
        # Process the data
        result = await orchestrator.execute(input_object=test_data, agent_input={"selected_kpis": ["sales", "profit"]})
        
        # Check visualization results
        viz_results = result.get("visualization_results", {})
        total_plots = viz_results.get("total_plots", 0)
        
        logger.info(f"✅ Generated {total_plots} plots (minimal set)")
        
        if "optimization_note" in viz_results:
            logger.info(f"📝 {viz_results['optimization_note']}")
        
        # Show plot categories
        if "plots" in viz_results:
            plot_names = list(viz_results["plots"].keys())
            logger.info(f"📊 Plot types generated: {plot_names}")
        
        # Check storyteller status
        storyteller_result = result.get("agent_results", {}).get("storyteller", {})
        if storyteller_result.get("status") == "completed":
            logger.info("✅ Real StorytellerAgent executed successfully!")
        
        return result
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return None

if __name__ == "__main__":
    result = asyncio.run(test_minimal_visualization())
    
    if result:
        print("\n🎉 Test completed successfully!")
        
        # Show results summary
        viz_data = result.get("visualization_results", {})
        print(f"\n📊 Visualization Summary:")
        print(f"   Total Plots: {viz_data.get('total_plots', 0)}")
        print(f"   Data Rows: {viz_data.get('data_summary', {}).get('total_rows', 'N/A')}")
        print(f"   Data Columns: {viz_data.get('data_summary', {}).get('total_columns', 'N/A')}")
        
        storyteller_data = result.get("agent_results", {}).get("storyteller", {})
        print(f"\n📖 Storyteller Status: {storyteller_data.get('status', 'unknown')}")
        
        if viz_data.get("total_plots", 0) <= 10:
            print("✅ Plot count is reasonable - should not crash the page!")
        else:
            print("⚠️ Plot count might still be too high")
    else:
        print("\n❌ Test failed!")
