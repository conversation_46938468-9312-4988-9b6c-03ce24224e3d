"""
Debug script to inspect the actual result structure from the platform
"""
import asyncio
import json
from main_platform import AIDataAnalysisPlatform

async def debug_result_structure():
    """Debug the actual result structure"""
    print("🔍 Debugging result structure...")
    
    # Use a simple test file
    test_csv = "date,revenue,customers\n2024-01-01,1000,50\n2024-01-02,1200,60"
    
    platform = AIDataAnalysisPlatform()
    
    try:
        result = await platform.analyze_business_data(
            file_path='debug.csv',
            file_content=test_csv.encode('utf-8')
        )
        
        print("\n📋 RESULT STRUCTURE:")
        print("=" * 50)
        
        # Print top-level keys
        print("🔑 Top-level keys:")
        for key in result.keys():
            print(f"  - {key}")
        
        # Check for visualization data
        print("\n🎨 VISUALIZATION DATA LOCATIONS:")
        
        # Check in agent_orchestration
        if 'agent_orchestration' in result:
            print("✅ Found agent_orchestration")
            agent_orch = result['agent_orchestration']
            if isinstance(agent_orch, dict):
                print("  🔑 agent_orchestration keys:")
                for key in agent_orch.keys():
                    print(f"    - {key}")
                
                if 'visualization_results' in agent_orch:
                    viz_results = agent_orch['visualization_results']
                    print("  ✅ Found visualization_results")
                    print(f"    📊 Type: {type(viz_results)}")
                    if isinstance(viz_results, dict):
                        print("    🔑 visualization_results keys:")
                        for key in viz_results.keys():
                            print(f"      - {key}")
                        
                        # Check total_plots
                        total_plots = viz_results.get('total_plots', 0)
                        print(f"    📈 Total plots: {total_plots}")
                        
                        # Check if there are actual plot HTML content
                        if 'plots' in viz_results:
                            plots = viz_results['plots']
                            print(f"    🎯 Number of plot HTML files: {len(plots) if isinstance(plots, dict) else 'Not a dict'}")
                            if isinstance(plots, dict):
                                print("    📊 Available plots:")
                                for plot_name in list(plots.keys())[:5]:  # Show first 5
                                    print(f"      - {plot_name}")
                                if len(plots) > 5:
                                    print(f"      ... and {len(plots) - 5} more")
        
        # Check for direct comprehensive_visualizations
        if 'comprehensive_visualizations' in result:
            print("✅ Found comprehensive_visualizations")
        else:
            print("❌ No comprehensive_visualizations key found")
        
        print("\n🧪 SAMPLE JSON STRUCTURE (first 500 chars):")
        json_str = json.dumps(result, default=str)[:500]
        print(json_str + "...")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await platform.close()

if __name__ == "__main__":
    asyncio.run(debug_result_structure())
