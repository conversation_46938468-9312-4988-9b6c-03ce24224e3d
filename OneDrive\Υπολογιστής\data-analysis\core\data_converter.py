#!/usr/bin/env python3
"""
Data Converter: Robust conversion layer for all file types to standardized JSON format
Handles Excel, CSV, JSON, and other formats with comprehensive error handling
"""

import pandas as pd
import json
import io
import logging
from typing import Dict, Any, List, Union, Optional
from pathlib import Path
import numpy as np
from datetime import datetime, date
import decimal

logger = logging.getLogger(__name__)

class DataConverter:
    """
    Robust data converter that standardizes all file formats to JSON
    """
    
    def __init__(self):
        self.supported_formats = {
            '.xlsx': self._convert_excel,
            '.xls': self._convert_excel,
            '.csv': self._convert_csv,
            '.json': self._convert_json,
            '.txt': self._convert_txt,
            '.tsv': self._convert_tsv
        }
    
    def convert_to_standard_format(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """
        Convert any supported file format to standardized JSON format
        
        Returns:
        {
            "records": [{"col1": "val1", "col2": "val2"}, ...],
            "columns": ["col1", "col2", ...],
            "metadata": {
                "total_rows": int,
                "total_columns": int,
                "file_type": str,
                "conversion_timestamp": str,
                "data_types": {...}
            }
        }
        """
        try:
            logger.info(f"🔄 Converting file: {filename}")
            
            # Determine file type
            file_extension = Path(filename).suffix.lower()
            
            if file_extension not in self.supported_formats:
                raise ValueError(f"Unsupported file format: {file_extension}")
            
            # Convert using appropriate method
            converter_func = self.supported_formats[file_extension]
            df = converter_func(file_content, filename)
            
            # Standardize DataFrame
            df_clean = self._clean_dataframe(df)
            
            # Convert to standard format
            standard_format = self._dataframe_to_standard_format(df_clean, filename, file_extension)
            
            logger.info(f"✅ Successfully converted {filename}: {standard_format['metadata']['total_rows']} rows, {standard_format['metadata']['total_columns']} columns")
            
            return standard_format
            
        except Exception as e:
            logger.error(f"❌ Failed to convert {filename}: {e}")
            return self._create_error_response(filename, str(e))
    
    def _convert_excel(self, file_content: bytes, filename: str) -> pd.DataFrame:
        """Convert Excel files to DataFrame"""
        logger.info(f"📊 Converting Excel file: {filename}")
        
        try:
            # Try to read Excel file
            df = pd.read_excel(io.BytesIO(file_content), engine='openpyxl')
            logger.info(f"✅ Excel conversion successful: {df.shape}")
            return df
            
        except Exception as e:
            logger.warning(f"⚠️ openpyxl failed, trying xlrd: {e}")
            try:
                df = pd.read_excel(io.BytesIO(file_content), engine='xlrd')
                logger.info(f"✅ Excel conversion successful with xlrd: {df.shape}")
                return df
            except Exception as e2:
                logger.error(f"❌ Both Excel engines failed: {e2}")
                raise ValueError(f"Failed to read Excel file: {e2}")
    
    def _convert_csv(self, file_content: bytes, filename: str) -> pd.DataFrame:
        """Convert CSV files to DataFrame"""
        logger.info(f"📄 Converting CSV file: {filename}")
        
        try:
            # Try different encodings and separators
            encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
            separators = [',', ';', '\t', '|']
            
            for encoding in encodings:
                for sep in separators:
                    try:
                        content_str = file_content.decode(encoding)
                        df = pd.read_csv(io.StringIO(content_str), sep=sep)
                        
                        # Check if we got meaningful data (more than 1 column)
                        if df.shape[1] > 1:
                            logger.info(f"✅ CSV conversion successful with encoding={encoding}, sep='{sep}': {df.shape}")
                            return df
                            
                    except Exception:
                        continue
            
            # Fallback: try with default settings
            content_str = file_content.decode('utf-8', errors='ignore')
            df = pd.read_csv(io.StringIO(content_str))
            logger.info(f"✅ CSV conversion successful with fallback: {df.shape}")
            return df
            
        except Exception as e:
            logger.error(f"❌ CSV conversion failed: {e}")
            raise ValueError(f"Failed to read CSV file: {e}")
    
    def _convert_json(self, file_content: bytes, filename: str) -> pd.DataFrame:
        """Convert JSON files to DataFrame"""
        logger.info(f"🔗 Converting JSON file: {filename}")
        
        try:
            content_str = file_content.decode('utf-8')
            data = json.loads(content_str)
            
            # Handle different JSON structures
            if isinstance(data, list):
                df = pd.DataFrame(data)
            elif isinstance(data, dict):
                if 'data' in data:
                    df = pd.DataFrame(data['data'])
                elif 'records' in data:
                    df = pd.DataFrame(data['records'])
                else:
                    df = pd.DataFrame([data])
            else:
                raise ValueError("Unsupported JSON structure")
            
            logger.info(f"✅ JSON conversion successful: {df.shape}")
            return df
            
        except Exception as e:
            logger.error(f"❌ JSON conversion failed: {e}")
            raise ValueError(f"Failed to read JSON file: {e}")
    
    def _convert_txt(self, file_content: bytes, filename: str) -> pd.DataFrame:
        """Convert TXT files to DataFrame (assume tab-separated)"""
        logger.info(f"📝 Converting TXT file: {filename}")
        return self._convert_csv(file_content, filename)  # Use CSV converter with different separators
    
    def _convert_tsv(self, file_content: bytes, filename: str) -> pd.DataFrame:
        """Convert TSV files to DataFrame"""
        logger.info(f"📋 Converting TSV file: {filename}")
        
        try:
            content_str = file_content.decode('utf-8')
            df = pd.read_csv(io.StringIO(content_str), sep='\t')
            logger.info(f"✅ TSV conversion successful: {df.shape}")
            return df
            
        except Exception as e:
            logger.error(f"❌ TSV conversion failed: {e}")
            raise ValueError(f"Failed to read TSV file: {e}")
    
    def _clean_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and standardize DataFrame"""
        logger.info(f"🧹 Cleaning DataFrame with shape: {df.shape}")
        
        # Remove completely empty rows and columns
        df = df.dropna(how='all').dropna(axis=1, how='all')
        
        # Clean column names
        df.columns = [str(col).strip() for col in df.columns]
        
        # Handle duplicate column names
        cols = pd.Series(df.columns)
        for dup in cols[cols.duplicated()].unique():
            cols[cols[cols == dup].index.values.tolist()] = [dup + '_' + str(i) if i != 0 else dup for i in range(sum(cols == dup))]
        df.columns = cols
        
        # Convert data types appropriately
        df = self._optimize_dtypes(df)
        
        logger.info(f"✅ DataFrame cleaned: {df.shape}")
        return df
    
    def _optimize_dtypes(self, df: pd.DataFrame) -> pd.DataFrame:
        """Optimize DataFrame data types"""
        for col in df.columns:
            # Try to convert to numeric if possible
            if df[col].dtype == 'object':
                try:
                    # Try integer first
                    df[col] = pd.to_numeric(df[col], downcast='integer')
                except:
                    try:
                        # Try float
                        df[col] = pd.to_numeric(df[col], downcast='float')
                    except:
                        # Try datetime
                        try:
                            df[col] = pd.to_datetime(df[col])
                        except:
                            # Keep as string
                            pass
        
        return df
    
    def _dataframe_to_standard_format(self, df: pd.DataFrame, filename: str, file_extension: str) -> Dict[str, Any]:
        """Convert DataFrame to standardized JSON format"""
        
        # Convert DataFrame to records (list of dictionaries)
        records = df.to_dict('records')
        
        # Make records JSON serializable
        records = self._make_json_serializable(records)
        
        # Get column information
        columns = list(df.columns)
        
        # Analyze data types
        data_types = {}
        for col in df.columns:
            dtype_str = str(df[col].dtype)
            if 'int' in dtype_str:
                data_types[col] = 'integer'
            elif 'float' in dtype_str:
                data_types[col] = 'float'
            elif 'datetime' in dtype_str:
                data_types[col] = 'datetime'
            elif 'bool' in dtype_str:
                data_types[col] = 'boolean'
            else:
                data_types[col] = 'string'
        
        # Create metadata
        metadata = {
            "total_rows": len(df),
            "total_columns": len(df.columns),
            "file_type": file_extension,
            "filename": filename,
            "conversion_timestamp": datetime.now().isoformat(),
            "data_types": data_types,
            "column_names": columns,
            "conversion_success": True
        }
        
        return {
            "records": records,
            "columns": columns,
            "metadata": metadata
        }
    
    def _make_json_serializable(self, obj):
        """Make object JSON serializable"""
        if isinstance(obj, list):
            return [self._make_json_serializable(item) for item in obj]
        elif isinstance(obj, dict):
            return {k: self._make_json_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, (pd.Timestamp, datetime, date)):
            return obj.isoformat()
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, (np.integer, np.int64, np.int32)):
            return int(obj)
        elif isinstance(obj, (np.floating, np.float64, np.float32)):
            return float(obj)
        elif isinstance(obj, decimal.Decimal):
            return float(obj)
        elif pd.isna(obj):
            return None
        else:
            return obj
    
    def _create_error_response(self, filename: str, error_message: str) -> Dict[str, Any]:
        """Create error response in standard format"""
        return {
            "records": [],
            "columns": [],
            "metadata": {
                "total_rows": 0,
                "total_columns": 0,
                "file_type": "unknown",
                "filename": filename,
                "conversion_timestamp": datetime.now().isoformat(),
                "data_types": {},
                "column_names": [],
                "conversion_success": False,
                "error": error_message
            }
        }
