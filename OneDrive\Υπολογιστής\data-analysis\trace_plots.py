#!/usr/bin/env python3
"""
Targeted test to trace what happens to plots during processing
"""

import asyncio
import pandas as pd
from core.visualization_engine import AdvancedVisualizationEngine
from web_interface import make_json_serializable

async def trace_plot_journey():
    """Trace what happens to plots through the entire pipeline"""
    print("🔍 Tracing plot journey through pipeline...")
    
    # Step 1: Create test data
    print("\n📊 Step 1: Creating test data...")
    test_data = pd.DataFrame({
        'Sales': [100, 150, 200, 120, 180, 190, 210, 140, 160, 175],
        'Region': ['North', 'South', 'East', 'West', 'Central', 'North', 'South', 'East', 'West', 'Central'],
        'Revenue': [1000, 1500, 2000, 1200, 1800, 1900, 2100, 1400, 1600, 1750],
        'Date': pd.date_range('2024-01-01', periods=10)
    })
    print(f"✅ Test data created: {test_data.shape}")
    
    # Step 2: Generate visualizations directly
    print("\n🎨 Step 2: Generating visualizations...")
    viz_engine = AdvancedVisualizationEngine()
    viz_result = viz_engine.generate_comprehensive_plots(test_data)
    
    print(f"✅ Visualization result type: {type(viz_result)}")
    print(f"✅ Total plots: {viz_result.get('total_plots', 0)}")
    
    plots = viz_result.get('plots', {})
    print(f"✅ Plots dict type: {type(plots)}")
    print(f"✅ Number of plots: {len(plots)}")
    
    if plots:
        first_plot_name = list(plots.keys())[0]
        first_plot = plots[first_plot_name]
        print(f"✅ First plot '{first_plot_name}':")
        print(f"   Type: {type(first_plot)}")
        print(f"   Length: {len(str(first_plot)) if first_plot else 0}")
        print(f"   Contains HTML: {'<div>' in str(first_plot).lower()}")
        
        # Show a snippet
        plot_str = str(first_plot)
        if len(plot_str) > 100:
            print(f"   Preview: {plot_str[:100]}...")
        else:
            print(f"   Content: {plot_str}")
    
    # Step 3: Test JSON serialization
    print("\n🧪 Step 3: Testing JSON serialization...")
    try:
        serialized = make_json_serializable(viz_result)
        print(f"✅ Serialization successful: {type(serialized)}")
        
        serialized_plots = serialized.get('plots', {})
        print(f"✅ Serialized plots type: {type(serialized_plots)}")
        print(f"✅ Serialized plots count: {len(serialized_plots)}")
        
        if serialized_plots and plots:
            print(f"✅ Plot survived: {first_plot_name in serialized_plots}")
            if first_plot_name in serialized_plots:
                survived_plot = serialized_plots[first_plot_name]
                print(f"✅ Survived plot type: {type(survived_plot)}")
                print(f"✅ Survived plot length: {len(str(survived_plot))}")
                print(f"✅ Still contains HTML: {'<div>' in str(survived_plot).lower()}")
        
        # Check if 'plots' key exists at all
        if 'plots' not in serialized:
            print("❌ 'plots' key missing from serialized result!")
        elif not serialized['plots']:
            print("❌ 'plots' key exists but is empty!")
            
    except Exception as e:
        print(f"❌ Serialization failed: {e}")
        import traceback
        traceback.print_exc()
    
    # Step 4: Test with the orchestrator flow
    print("\n🤖 Step 4: Testing with orchestrator...")
    from core.enhanced_orchestrator_clean import EnhancedAgentUniverseOrchestrator
    
    orchestrator = EnhancedAgentUniverseOrchestrator()
    
    # Prepare data as it would come from file handler
    file_data = {
        "data": test_data,
        "records": test_data.to_dict('records'),
        "columns": list(test_data.columns),
        "row_count": len(test_data)
    }
    
    orchestrator_input = {
        "uploaded_file_data": file_data,
        "file_metadata": {"filename": "test.csv"},
        "deepseek_analysis": {"business_context": {"industry": "Test"}}
    }
    
    try:
        orch_result = await orchestrator.execute(None, orchestrator_input)
        print(f"✅ Orchestrator result type: {type(orch_result)}")
        
        if 'visualization_results' in orch_result:
            viz_res = orch_result['visualization_results']
            print(f"✅ Visualization results in orchestrator: {viz_res.get('total_plots', 0)} plots")
            
            orch_plots = viz_res.get('plots', {})
            print(f"✅ Orchestrator plots count: {len(orch_plots)}")
            
            if orch_plots:
                print("✅ Plots preserved in orchestrator result!")
            else:
                print("❌ Plots lost in orchestrator!")
                
        else:
            print("❌ No visualization_results in orchestrator result!")
            
    except Exception as e:
        print(f"❌ Orchestrator test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(trace_plot_journey())
