"""
Advanced Visualization Engine for AI Data Analysis Platform
Generates comprehensive plots automatically based on data characteristics

This module creates as many relevant visualizations as possible from your data:
- Automatic plot type selection based on data types
- Interactive Plotly charts for web display
- Statistical plots for deeper insights
- Business intelligence visualizations
- Time series analysis plots
- Correlation and relationship analysis
- Distribution analysis
- Trend analysis
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
import plotly.figure_factory as ff
from plotly.subplots import make_subplots
import base64
import io
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

class AdvancedVisualizationEngine:
    """
    Revolutionary visualization engine that automatically generates 
    comprehensive plots based on data characteristics
    """
    def __init__(self):
        """Initialize the visualization engine"""
        # Set modern styling
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
        # Plotly template for consistent styling
        self.plotly_template = "plotly_white"
        
        logger.info("🎨 Advanced Visualization Engine initialized")
    
    def _make_json_serializable(self, obj) -> Any:
        """Convert numpy/pandas data types to JSON serializable Python types"""
        if isinstance(obj, np.ndarray):
            return obj.tolist()  # Convert NumPy arrays to lists first
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, pd.Timestamp):
            return obj.isoformat()
        elif isinstance(obj, tuple):
            return list(obj)
        elif hasattr(obj, 'dtype') and hasattr(obj, 'tolist'):  # Handle pandas/numpy objects with tolist method
            try:
                return obj.tolist()
            except:
                return str(obj)
        elif hasattr(obj, 'dtype'):  # Handle other pandas/numpy dtypes
            return str(obj)
        elif isinstance(obj, type) and hasattr(obj, '__module__') and 'numpy' in str(obj.__module__):
            return str(obj)
        elif isinstance(obj, dict):
            return {key: self._make_json_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._make_json_serializable(item) for item in obj]
        else:
            # Try to serialize, fallback to string representation
            try:
                import json
                json.dumps(obj)
                return obj
            except (TypeError, ValueError):
                # Check if it's a NumPy type we missed
                if hasattr(obj, 'tolist'):
                    try:
                        return obj.tolist()
                    except:
                        pass
                return str(obj)
    
    def generate_comprehensive_plots(self, df: pd.DataFrame, file_metadata: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Generate essential visualizations (reduced set to prevent crashes)
        
        Args:
            df: DataFrame to visualize
            file_metadata: Metadata about the file
            
        Returns:
            Dictionary containing essential generated plots
        """       
        
        try:
            logger.info(f"🎨 Generating essential plots for data with shape {df.shape}")
            
            plots = {}
            
            # Data preprocessing
            df_clean = self._preprocess_data(df)
            
            # 1. OVERVIEW PLOTS (Essential only)
            try:
                plots.update(self._generate_overview_plots_minimal(df_clean))
                logger.info("✅ Essential overview plots generated")
            except Exception as e:
                logger.error(f"❌ Overview plots failed: {e}")
            
            # 2. NUMERIC ANALYSIS PLOTS (Top 2-3 most important)
            numeric_cols = df_clean.select_dtypes(include=[np.number]).columns.tolist()
            if len(numeric_cols) > 0:
                try:
                    plots.update(self._generate_numeric_plots_minimal(df_clean, numeric_cols))
                    logger.info("✅ Essential numeric plots generated")
                except Exception as e:
                    logger.error(f"❌ Numeric plots failed: {e}")
            
            # 3. CATEGORICAL ANALYSIS PLOTS (Essential only)
            categorical_cols = df_clean.select_dtypes(include=['object', 'category']).columns.tolist()
            if len(categorical_cols) > 0:
                try:
                    plots.update(self._generate_categorical_plots_minimal(df_clean, categorical_cols))
                    logger.info("✅ Essential categorical plots generated")
                except Exception as e:
                    logger.error(f"❌ Categorical plots failed: {e}")
            
            # 4. TIME SERIES PLOTS (Only if time data detected)
            date_cols = self._detect_date_columns(df_clean)
            if len(date_cols) > 0:
                try:
                    plots.update(self._generate_time_series_plots_minimal(df_clean, date_cols, numeric_cols))
                    logger.info("✅ Essential time series plots generated")
                except Exception as e:
                    logger.error(f"❌ Time series plots failed: {e}")
            
            # 5. CORRELATION PLOTS (Only if multiple numeric columns)
            if len(numeric_cols) > 1:
                try:
                    plots.update(self._generate_correlation_plots_minimal(df_clean, numeric_cols))
                    logger.info("✅ Essential correlation plots generated")
                except Exception as e:
                    logger.error(f"❌ Correlation plots failed: {e}")
            
            # Skip the heavy plot categories to prevent crashes:
            # - Distribution plots (too many variations)
            # - Business Intelligence plots (complex)
            # - Advanced Statistical plots (computationally expensive)
            # - Interactive Dashboard plots (memory intensive)
            
            logger.info(f"✅ Generated {len(plots)} essential plots (optimized for performance)")
            
            result = {
                "total_plots": len(plots),
                "plot_categories": self._categorize_plots(plots),
                "plots": plots,
                "data_summary": {
                    "total_rows": len(df_clean),
                    "total_columns": len(df_clean.columns),
                    "numeric_columns": len(numeric_cols),
                    "categorical_columns": len(categorical_cols),
                    "date_columns": len(date_cols)
                },
                "optimization_note": "Reduced plot set to prevent page crashes"
            }
              # DON'T serialize here - let the web interface handle JSON serialization
            # This preserves the raw plot HTML content
            return result
            
        except Exception as e:
            logger.error(f"❌ Visualization generation failed: {e}")
            return {
                "total_plots": 0,
                "error": str(e),
                "plots": {}
            }
    
    def _preprocess_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Preprocess data for better visualization"""
        df_clean = df.copy()
        
        # Convert string dates to datetime
        for col in df_clean.columns:
            if df_clean[col].dtype == 'object':
                # Try to convert to datetime
                try:
                    pd.to_datetime(df_clean[col], errors='raise')
                    df_clean[col] = pd.to_datetime(df_clean[col])
                except:
                    pass
        
        return df_clean
    
    def _detect_date_columns(self, df: pd.DataFrame) -> List[str]:
        """Detect date/datetime columns"""
        date_cols = []
        
        for col in df.columns:
            if pd.api.types.is_datetime64_any_dtype(df[col]):
                date_cols.append(col)
            elif df[col].dtype == 'object':
                # Check if it looks like dates
                sample = df[col].dropna().astype(str).iloc[:10]
                try:
                    pd.to_datetime(sample, errors='raise')
                    date_cols.append(col)
                except:
                    pass
        
        return plots

    def _generate_overview_plots_minimal(self, df: pd.DataFrame) -> Dict:
        """Generate minimal overview plots - essential only"""
        plots = {}

        try:
            # 1. Data Shape Overview (simplified)
            fig = go.Figure(data=[
                go.Bar(
                    x=['Total Rows', 'Total Columns'],
                    y=[len(df), len(df.columns)],
                    marker_color=['#4ECDC4', '#44A08D']
                )
            ])
            fig.update_layout(
                title="Dataset Overview",
                template=self.plotly_template,
                height=300
            )
            plots['data_overview'] = fig.to_html(include_plotlyjs=True, div_id="data_overview")

        except Exception as e:
            logger.error(f"❌ Minimal overview plot failed: {e}")

        return plots

    def _generate_overview_plots(self, df: pd.DataFrame) -> Dict:
        """Generate overview plots"""
        plots = {}
        
        # 1. Data Shape Overview
        fig = go.Figure(data=[
            go.Bar(name='Dimensions', x=['Rows', 'Columns'], y=[len(df), len(df.columns)],
                   marker_color=['#FF6B6B', '#4ECDC4'])
        ])
        fig.update_layout(
            title="📊 Data Overview: Rows vs Columns",
            template=self.plotly_template,
            height=400
        )
        plots['data_overview'] = fig.to_html(include_plotlyjs=True, div_id="data_overview")
        
        # 2. Data Types Distribution
        type_counts = df.dtypes.value_counts()
        fig = px.pie(
            values=type_counts.values,
            names=[str(dtype) for dtype in type_counts.index],  # Convert dtypes to strings
            title="🔢 Data Types Distribution"
        )
        fig.update_layout(template=self.plotly_template, height=400)
        plots['data_types'] = fig.to_html(include_plotlyjs=True, div_id="data_types")
        
        # 3. Missing Data Heatmap
        if df.isnull().sum().sum() > 0:
            missing_data = df.isnull().sum().sort_values(ascending=False)
            missing_data = missing_data[missing_data > 0]
            
            if len(missing_data) > 0:
                fig = px.bar(
                    x=missing_data.index, 
                    y=missing_data.values,
                    title="🚨 Missing Data Analysis",
                    labels={'x': 'Columns', 'y': 'Missing Values'}
                )
                fig.update_layout(template=self.plotly_template, height=400)
                plots['missing_data'] = fig.to_html(include_plotlyjs=True, div_id="missing_data")
        
        return plots

    def _generate_numeric_plots_minimal(self, df: pd.DataFrame, numeric_cols: List[str]) -> Dict:
        """Generate minimal numeric plots - essential only"""
        plots = {}

        try:
            # Only create a simple histogram for the first numeric column
            if len(numeric_cols) > 0:
                col = numeric_cols[0]
                fig = go.Figure(data=[
                    go.Histogram(x=df[col], name=col, marker_color='#4ECDC4')
                ])
                fig.update_layout(
                    title=f"Distribution of {col}",
                    template=self.plotly_template,
                    height=300
                )
                plots[f'hist_{col}'] = fig.to_html(include_plotlyjs=True, div_id=f"hist_{col}")

        except Exception as e:
            logger.error(f"❌ Minimal numeric plot failed: {e}")

        return plots

    def _generate_numeric_plots(self, df: pd.DataFrame, numeric_cols: List[str]) -> Dict:
        """Generate comprehensive numeric analysis plots"""
        plots = {}
        
        # 1. Numeric Summary Statistics
        stats_df = df[numeric_cols].describe()
        fig = go.Figure(data=go.Heatmap(
            z=stats_df.values,
            x=stats_df.columns,
            y=stats_df.index,
            colorscale='Viridis',
            text=stats_df.round(2).values,
            texttemplate="%{text}",
            textfont={"size": 10}
        ))
        fig.update_layout(
            title="📈 Numeric Columns: Statistical Summary",
            template=self.plotly_template,
            height=500
        )
        plots['numeric_summary'] = fig.to_html(include_plotlyjs=True, div_id="numeric_summary")
        
        # 2. Individual Numeric Column Analysis
        for i, col in enumerate(numeric_cols[:6]):  # Limit to first 6 columns
            # Box plot
            fig = px.box(df, y=col, title=f"📊 {col}: Distribution Analysis")
            fig.update_layout(template=self.plotly_template, height=400)
            plots[f'box_{col}'] = fig.to_html(include_plotlyjs=True, div_id=f"box_{col}")
            
            # Histogram with KDE
            fig = px.histogram(df, x=col, marginal="box", title=f"📊 {col}: Frequency Distribution")
            fig.update_layout(template=self.plotly_template, height=400)
            plots[f'hist_{col}'] = fig.to_html(include_plotlyjs=True, div_id=f"hist_{col}")
        
        # 3. Multi-column comparison
        if len(numeric_cols) > 1:
            # Parallel coordinates
            fig = px.parallel_coordinates(
                df, 
                dimensions=numeric_cols[:6],  # Limit to 6 dimensions
                title="🌈 Multi-Dimensional Analysis: Parallel Coordinates"
            )
            fig.update_layout(template=self.plotly_template, height=500)
            plots['parallel_coords'] = fig.to_html(include_plotlyjs=True, div_id="parallel_coords")
        
        return plots

    def _generate_categorical_plots_minimal(self, df: pd.DataFrame, categorical_cols: List[str]) -> Dict:
        """Generate minimal categorical plots - essential only"""
        plots = {}

        try:
            # Only create a simple bar chart for the first categorical column
            if len(categorical_cols) > 0:
                col = categorical_cols[0]
                value_counts = df[col].value_counts().head(5)  # Top 5 categories only

                fig = go.Figure(data=[
                    go.Bar(x=value_counts.index, y=value_counts.values, marker_color='#44A08D')
                ])
                fig.update_layout(
                    title=f"Top Categories in {col}",
                    template=self.plotly_template,
                    height=300
                )
                plots[f'bar_{col}'] = fig.to_html(include_plotlyjs=True, div_id=f"bar_{col}")

        except Exception as e:
            logger.error(f"❌ Minimal categorical plot failed: {e}")

        return plots

    def _generate_categorical_plots(self, df: pd.DataFrame, categorical_cols: List[str]) -> Dict:
        """Generate categorical analysis plots"""
        plots = {}
        
        for i, col in enumerate(categorical_cols[:5]):  # Limit to first 5 columns
            value_counts = df[col].value_counts().head(10)  # Top 10 categories
            
            # Bar chart
            fig = px.bar(
                x=value_counts.index, 
                y=value_counts.values,
                title=f"📊 {col}: Category Distribution",
                labels={'x': col, 'y': 'Count'}
            )
            fig.update_layout(template=self.plotly_template, height=400)
            plots[f'cat_bar_{col}'] = fig.to_html(include_plotlyjs=True, div_id=f"cat_bar_{col}")
            
            # Pie chart
            fig = px.pie(
                values=value_counts.values, 
                names=value_counts.index,
                title=f"🥧 {col}: Proportion Analysis"
            )
            fig.update_layout(template=self.plotly_template, height=400)
            plots[f'cat_pie_{col}'] = fig.to_html(include_plotlyjs=True, div_id=f"cat_pie_{col}")
        
        return plots

    def _generate_time_series_plots_minimal(self, df: pd.DataFrame, date_cols: List[str], numeric_cols: List[str]) -> Dict:
        """Generate minimal time series plots - essential only"""
        plots = {}

        try:
            # Only create one simple time series plot
            if len(date_cols) > 0 and len(numeric_cols) > 0:
                date_col = date_cols[0]
                num_col = numeric_cols[0]

                df_sorted = df.sort_values(date_col)

                fig = go.Figure(data=[
                    go.Scatter(x=df_sorted[date_col], y=df_sorted[num_col],
                             mode='lines+markers', name=f'{num_col} over time',
                             line=dict(color='#4ECDC4'))
                ])
                fig.update_layout(
                    title=f"{num_col} Trend Over Time",
                    template=self.plotly_template,
                    height=300
                )
                plots[f'trend_{num_col}'] = fig.to_html(include_plotlyjs=True, div_id=f"trend_{num_col}")

        except Exception as e:
            logger.error(f"❌ Minimal time series plot failed: {e}")

        return plots

    def _generate_time_series_plots(self, df: pd.DataFrame, date_cols: List[str], numeric_cols: List[str]) -> Dict:
        """Generate time series analysis plots"""
        plots = {}
        
        for date_col in date_cols[:2]:  # Limit to first 2 date columns
            df_sorted = df.sort_values(date_col)
            
            # Time series line plots for numeric columns
            for num_col in numeric_cols[:3]:  # Limit to first 3 numeric columns
                fig = px.line(
                    df_sorted, 
                    x=date_col, 
                    y=num_col,
                    title=f"📈 Time Series: {num_col} over {date_col}"
                )
                fig.update_layout(template=self.plotly_template, height=400)
                plots[f'timeseries_{num_col}_{date_col}'] = fig.to_html(include_plotlyjs=True, div_id=f"timeseries_{num_col}_{date_col}")
                
                # Moving average if enough data points
                if len(df_sorted) > 10:
                    df_sorted[f'{num_col}_ma'] = df_sorted[num_col].rolling(window=min(7, len(df_sorted)//3)).mean()
                    
                    fig = go.Figure()
                    fig.add_trace(go.Scatter(
                        x=df_sorted[date_col], 
                        y=df_sorted[num_col],
                        mode='lines', 
                        name='Actual',
                        line=dict(color='#FF6B6B')
                    ))
                    fig.add_trace(go.Scatter(
                        x=df_sorted[date_col], 
                        y=df_sorted[f'{num_col}_ma'],
                        mode='lines', 
                        name='Moving Average',
                        line=dict(color='#4ECDC4', width=3)
                    ))
                    fig.update_layout(
                        title=f"📈 Trend Analysis: {num_col} with Moving Average",
                        template=self.plotly_template,
                        height=400
                    )
                    plots[f'trend_{num_col}_{date_col}'] = fig.to_html(include_plotlyjs=True, div_id=f"trend_{num_col}_{date_col}")
        
        return plots

    def _generate_correlation_plots_minimal(self, df: pd.DataFrame, numeric_cols: List[str]) -> Dict:
        """Generate minimal correlation plots - essential only"""
        plots = {}

        try:
            # Only create a simple correlation heatmap
            if len(numeric_cols) > 1:
                corr_matrix = df[numeric_cols].corr()

                fig = go.Figure(data=go.Heatmap(
                    z=corr_matrix.values,
                    x=corr_matrix.columns,
                    y=corr_matrix.columns,
                    colorscale='RdBu',
                    zmid=0
                ))
                fig.update_layout(
                    title="Correlation Matrix",
                    template=self.plotly_template,
                    height=300
                )
                plots['correlation_heatmap'] = fig.to_html(include_plotlyjs=True, div_id="correlation_heatmap")

        except Exception as e:
            logger.error(f"❌ Minimal correlation plot failed: {e}")

        return plots

    def _generate_correlation_plots(self, df: pd.DataFrame, numeric_cols: List[str]) -> Dict:
        """Generate correlation and relationship plots"""
        plots = {}
        
        # 1. Correlation Matrix Heatmap
        corr_matrix = df[numeric_cols].corr()
        fig = px.imshow(
            corr_matrix,
            text_auto=True,
            aspect="auto",
            title="🔗 Correlation Matrix: Relationship Strength",
            color_continuous_scale='RdBu'
        )
        fig.update_layout(template=self.plotly_template, height=500)
        plots['correlation_matrix'] = fig.to_html(include_plotlyjs=True, div_id="correlation_matrix")
          # 2. Pairwise Scatter Plots (for top correlated pairs)
        correlations = []
        for i in range(len(numeric_cols)):
            for j in range(i+1, len(numeric_cols)):
                try:
                    corr_val = corr_matrix.iloc[i, j]
                    if pd.notna(corr_val) and isinstance(corr_val, (int, float, np.number)):
                        corr_val = float(corr_val)  # Convert to Python float
                        if abs(corr_val) > 0.3:  # Only strong correlations
                            correlations.append((numeric_cols[i], numeric_cols[j], abs(corr_val)))
                except (ValueError, TypeError):
                    continue  # Skip non-numeric correlations
        
        # Sort by correlation strength and take top 5
        correlations.sort(key=lambda x: x[2], reverse=True)
        
        for i, (col1, col2, corr_val) in enumerate(correlations[:5]):
            fig = px.scatter(
                df, 
                x=col1, 
                y=col2,
                title=f"💫 Relationship: {col1} vs {col2} (r={corr_val:.3f})",
                trendline="ols"
            )
            fig.update_layout(template=self.plotly_template, height=400)
            plots[f'scatter_{col1}_{col2}'] = fig.to_html(include_plotlyjs=True, div_id=f"scatter_{col1}_{col2}")
        
        return plots
    
    def _generate_distribution_plots(self, df: pd.DataFrame, numeric_cols: List[str], categorical_cols: List[str]) -> Dict:
        """Generate distribution analysis plots"""
        plots = {}
        
        # 1. Multi-histogram for numeric columns
        if len(numeric_cols) > 1:
            # Calculate proper grid dimensions
            num_plots = min(6, len(numeric_cols))
            cols = min(2, num_plots)
            rows = (num_plots + cols - 1) // cols  # Ceiling division

            fig = make_subplots(
                rows=rows,
                cols=cols,
                subplot_titles=numeric_cols[:num_plots]
            )

            for i, col in enumerate(numeric_cols[:num_plots]):
                row = i // cols + 1
                col_num = i % cols + 1

                fig.add_trace(
                    go.Histogram(x=df[col], name=col, showlegend=False),
                    row=row, col=col_num
                )
            
            fig.update_layout(
                title="📊 Distribution Comparison: All Numeric Columns",
                template=self.plotly_template,
                height=600
            )
            plots['multi_histogram'] = fig.to_html(include_plotlyjs=True, div_id="multi_histogram")
        
        # 2. Box plots by category
        if len(categorical_cols) > 0 and len(numeric_cols) > 0:
            cat_col = categorical_cols[0]
            num_col = numeric_cols[0]
            
            fig = px.box(
                df, 
                x=cat_col, 
                y=num_col,
                title=f"📦 Distribution by Category: {num_col} by {cat_col}"
            )
            fig.update_layout(template=self.plotly_template, height=400)
            plots[f'box_by_category_{cat_col}_{num_col}'] = fig.to_html(include_plotlyjs=True, div_id=f"box_by_category_{cat_col}_{num_col}")
        
        return plots
    
    def _generate_business_intelligence_plots(self, df: pd.DataFrame, numeric_cols: List[str], categorical_cols: List[str]) -> Dict:
        """Generate business intelligence specific plots"""
        plots = {}
        
        # 1. KPI Dashboard Style Metrics
        if len(numeric_cols) > 0:
            # Create a metrics dashboard
            metrics = {}
            for col in numeric_cols[:4]:
                metrics[col] = {
                    'total': df[col].sum(),
                    'average': df[col].mean(),
                    'growth': self._calculate_growth_rate(df[col]) if len(df) > 1 else 0
                }
            
            # Metrics visualization
            metric_names = list(metrics.keys())
            totals = [metrics[col]['total'] for col in metric_names]
            
            fig = go.Figure(data=[
                go.Bar(name='Total Values', x=metric_names, y=totals, marker_color='#FF6B6B')
            ])
            fig.update_layout(
                title="💼 Business Metrics Dashboard",
                template=self.plotly_template,
                height=400
            )
            plots['business_metrics'] = fig.to_html(include_plotlyjs=True, div_id="business_metrics")
        
        # 2. Performance Analysis
        if len(numeric_cols) >= 2:
            # Performance scatter with size based on third metric
            x_col, y_col = numeric_cols[0], numeric_cols[1]
            size_col = numeric_cols[2] if len(numeric_cols) > 2 else None
            
            fig = px.scatter(
                df, 
                x=x_col, 
                y=y_col,
                size=size_col if size_col else None,
                title=f"🎯 Performance Analysis: {x_col} vs {y_col}",
                hover_data=numeric_cols[:4]
            )
            fig.update_layout(template=self.plotly_template, height=500)
            plots['performance_analysis'] = fig.to_html(include_plotlyjs=True, div_id="performance_analysis")
        
        return plots
    
    def _generate_statistical_plots(self, df: pd.DataFrame, numeric_cols: List[str]) -> Dict:
        """Generate advanced statistical plots"""
        plots = {}
        
        # 1. Q-Q Plots for normality testing
        for col in numeric_cols[:3]:
            data = df[col].dropna()
            if len(data) > 10:
                fig = go.Figure()
                
                # Generate Q-Q plot data
                sorted_data = np.sort(data)
                n = len(sorted_data)
                theoretical_quantiles = np.linspace(0.01, 0.99, n)
                theoretical_values = np.percentile(sorted_data, theoretical_quantiles * 100)
                
                fig.add_trace(go.Scatter(
                    x=theoretical_values,
                    y=sorted_data,
                    mode='markers',
                    name='Data Points',
                    marker=dict(color='#FF6B6B')
                ))
                
                # Add reference line
                min_val, max_val = min(theoretical_values), max(theoretical_values)
                fig.add_trace(go.Scatter(
                    x=[min_val, max_val],
                    y=[min_val, max_val],
                    mode='lines',
                    name='Normal Reference',
                    line=dict(color='#4ECDC4', dash='dash')
                ))
                
                fig.update_layout(
                    title=f"📊 Q-Q Plot: {col} (Normality Test)",
                    xaxis_title="Theoretical Quantiles",
                    yaxis_title="Sample Quantiles",
                    template=self.plotly_template,
                    height=400
                )
                plots[f'qq_plot_{col}'] = fig.to_html(include_plotlyjs=True, div_id=f"qq_plot_{col}")
        
        return plots
    
    def _generate_dashboard_plots(self, df: pd.DataFrame, numeric_cols: List[str], categorical_cols: List[str]) -> Dict:
        """Generate interactive dashboard-style plots"""
        plots = {}
        
        # 1. Executive Summary Dashboard
        if len(numeric_cols) > 0:
            # Create a comprehensive dashboard
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=[
                    "Key Metrics Overview",
                    "Trend Analysis", 
                    "Distribution Analysis",
                    "Performance Indicators"
                ],
                specs=[[{"type": "bar"}, {"type": "scatter"}],
                       [{"type": "histogram"}, {"type": "indicator"}]]
            )
            
            # Key metrics
            if len(numeric_cols) >= 1:
                col1 = numeric_cols[0]
                fig.add_trace(
                    go.Bar(x=['Total', 'Average', 'Max'], 
                          y=[df[col1].sum(), df[col1].mean(), df[col1].max()],
                          name=col1),
                    row=1, col=1
                )
            
            # Trend analysis
            if len(numeric_cols) >= 2:
                col2 = numeric_cols[1]
                fig.add_trace(
                    go.Scatter(x=list(range(len(df))), y=df[col2], 
                              mode='lines+markers', name=col2),
                    row=1, col=2
                )
            
            # Distribution
            if len(numeric_cols) >= 1:
                fig.add_trace(
                    go.Histogram(x=df[numeric_cols[0]], name='Distribution'),
                    row=2, col=1
                )
            
            # Performance indicator
            if len(numeric_cols) >= 1:
                avg_value = df[numeric_cols[0]].mean()
                fig.add_trace(
                    go.Indicator(
                        mode="gauge+number",
                        value=avg_value,
                        title={'text': f"Average {numeric_cols[0]}"},
                        gauge={'axis': {'range': [None, df[numeric_cols[0]].max()]},
                               'bar': {'color': "#FF6B6B"},
                               'steps': [{'range': [0, avg_value*0.8], 'color': "lightgray"},
                                        {'range': [avg_value*0.8, avg_value*1.2], 'color': "yellow"}],
                               'threshold': {'line': {'color': "red", 'width': 4},
                                           'thickness': 0.75, 'value': avg_value*1.1}}
                    ),
                    row=2, col=2
                )
            
            fig.update_layout(
                title="🎛️ Executive Dashboard: Complete Business Overview",
                template=self.plotly_template,
                height=700
            )
            plots['executive_dashboard'] = fig.to_html(include_plotlyjs=True, div_id="executive_dashboard")
        
        return plots
    
    def _calculate_growth_rate(self, series: pd.Series) -> float:
        """Calculate growth rate for a series"""
        try:
            if len(series) < 2:
                return 0
            first_val = series.iloc[0]
            last_val = series.iloc[-1]
            if first_val == 0:
                return 0
            return ((last_val - first_val) / abs(first_val)) * 100
        except:
            return 0
    
    def _categorize_plots(self, plots: Dict) -> Dict:
        """Categorize plots for better organization"""
        categories = {
            "overview": [],
            "numeric_analysis": [],
            "categorical_analysis": [],
            "time_series": [],
            "correlations": [],
            "distributions": [],
            "business_intelligence": [],
            "statistical_analysis": [],
            "dashboards": []
        }
        
        for plot_name in plots.keys():
            if any(x in plot_name for x in ['overview', 'data_types', 'missing']):
                categories["overview"].append(plot_name)
            elif any(x in plot_name for x in ['numeric', 'box_', 'hist_', 'parallel']):
                categories["numeric_analysis"].append(plot_name)
            elif any(x in plot_name for x in ['cat_', 'category']):
                categories["categorical_analysis"].append(plot_name)
            elif any(x in plot_name for x in ['timeseries', 'trend']):
                categories["time_series"].append(plot_name)
            elif any(x in plot_name for x in ['correlation', 'scatter']):
                categories["correlations"].append(plot_name)
            elif any(x in plot_name for x in ['distribution', 'multi_histogram', 'qq_plot']):
                categories["distributions"].append(plot_name)
            elif any(x in plot_name for x in ['business', 'metrics', 'performance']):
                categories["business_intelligence"].append(plot_name)
            elif any(x in plot_name for x in ['statistical', 'qq_']):
                categories["statistical_analysis"].append(plot_name)
            elif any(x in plot_name for x in ['dashboard']):
                categories["dashboards"].append(plot_name)
        
        return categories

# Initialize the visualization engine
visualization_engine = AdvancedVisualizationEngine()
