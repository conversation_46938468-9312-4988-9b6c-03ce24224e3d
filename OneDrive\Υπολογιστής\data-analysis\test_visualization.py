#!/usr/bin/env python3
"""
Test script to verify the visualization engine works independently
"""

import pandas as pd
import numpy as np
from core.visualization_engine import AdvancedVisualizationEngine

def test_visualization_engine():
    """Test the visualization engine with sample data"""
    print("🧪 Testing AdvancedVisualizationEngine...")
    
    # Create test data
    np.random.seed(42)
    dates = pd.date_range('2024-01-01', periods=50, freq='D')
    
    test_data = {
        'Date': dates,
        'Revenue': [10000 + i*100 + np.random.normal(0, 500) for i in range(50)],
        'Orders': [50 + i + np.random.randint(0, 20) for i in range(50)],
        'Customers': [30 + i + np.random.randint(0, 15) for i in range(50)],
        'Category': np.random.choice(['A', 'B', 'C'], 50).tolist(),
        'Region': np.random.choice(['North', 'South', 'East', 'West'], 50).tolist(),
    }
    
    df = pd.DataFrame(test_data)
    print(f"📊 Created test DataFrame with shape: {df.shape}")
    print(f"📊 Columns: {list(df.columns)}")
    print(f"📊 Data types: {df.dtypes.to_dict()}")
    
    # Initialize visualization engine
    viz_engine = AdvancedVisualizationEngine()
    print("✅ Visualization engine initialized")
    
    # Generate plots
    print("🎨 Generating comprehensive plots...")
    try:
        result = viz_engine.generate_comprehensive_plots(
            df, 
            file_metadata={
                "business_type": "Test Business",
                "agent_insights": {}
            }
        )
        
        print(f"✅ Visualization generation completed!")
        print(f"📊 Result type: {type(result)}")
        print(f"📊 Result keys: {list(result.keys()) if result else 'None'}")
        print(f"📊 Total plots: {result.get('total_plots', 'Not found')}")
        
        if result.get('plots'):
            plots = result['plots']
            print(f"📊 Plots dictionary keys: {list(plots.keys())}")
            print(f"📊 Number of plots: {len(plots)}")
            
            # Check first plot
            if plots:
                first_plot_name = list(plots.keys())[0]
                first_plot_html = plots[first_plot_name]
                print(f"📊 First plot '{first_plot_name}' HTML length: {len(first_plot_html) if first_plot_html else 0}")
                print(f"📊 First plot HTML starts with: {first_plot_html[:100] if first_plot_html else 'None'}...")
        
        return result
        
    except Exception as e:
        print(f"❌ Visualization generation failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = test_visualization_engine()
    
    if result and result.get('total_plots', 0) > 0:
        print("🎉 SUCCESS: Visualization engine is working!")
    else:
        print("💥 FAILURE: Visualization engine is not generating plots!")
