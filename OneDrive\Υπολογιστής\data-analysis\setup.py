#!/usr/bin/env python3
"""
Setup script for AI Data Analysis Platform

This script provides installation and dependency management for the
AgentUniverse-based AI Data Analysis Platform with DeepSeek LLM integration.
"""

from setuptools import setup, find_packages
import os

# Read the README file
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "AI Data Analysis Platform with AgentUniverse and DeepSeek LLMs"

# Read requirements from requirements.txt
def read_requirements():
    requirements_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    if os.path.exists(requirements_path):
        with open(requirements_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            # Filter out comments and empty lines
            return [line.strip() for line in lines 
                   if line.strip() and not line.strip().startswith('#')]
    return []

setup(
    name="ai-data-analysis-platform",
    version="1.0.0",
    description="Revolutionary AI Data Analysis Platform using AgentUniverse framework with DeepSeek LLMs",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    author="AI Data Analysis Team",
    author_email="<EMAIL>",
    url="https://github.com/your-org/ai-data-analysis-platform",
    
    # Package discovery
    packages=find_packages(exclude=['tests*', 'docs*']),
    
    # Include package data
    include_package_data=True,
    package_data={
        'agents': ['**/*.yaml', '**/*.yml'],
        '': ['*.toml', '*.txt', '*.md']
    },
    
    # Dependencies
    install_requires=read_requirements(),
    
    # Extra dependencies for different use cases
    extras_require={
        'dev': [
            'pytest>=7.4.0',
            'pytest-asyncio>=0.21.0',
            'black>=23.7.0',
            'flake8>=6.0.0',
            'mypy>=1.5.0',
            'pre-commit>=3.3.0'
        ],
        'ui': [
            'streamlit>=1.26.0',
            'gradio>=3.41.0'
        ],
        'advanced': [
            'torch>=2.0.0',
            'transformers>=4.33.0',
            'datasets>=2.14.0'
        ],
        'cloud': [
            'boto3>=1.28.0',
            'azure-storage-blob>=12.17.0',
            'google-cloud-storage>=2.10.0'
        ]
    },
    
    # Python version requirement
    python_requires='>=3.9',
    
    # Classification
    classifiers=[
        'Development Status :: 4 - Beta',
        'Intended Audience :: Developers',
        'Intended Audience :: Data Scientists',
        'License :: OSI Approved :: MIT License',
        'Programming Language :: Python :: 3',
        'Programming Language :: Python :: 3.9',
        'Programming Language :: Python :: 3.10',
        'Programming Language :: Python :: 3.11',
        'Programming Language :: Python :: 3.12',
        'Topic :: Scientific/Engineering :: Artificial Intelligence',
        'Topic :: Software Development :: Libraries :: Python Modules',
        'Topic :: Office/Business :: Financial :: Investment',
    ],
    
    # Keywords
    keywords='ai artificial-intelligence data-analysis business-intelligence agents llm deepseek agentuniverse',
    
    # Entry points for command-line tools
    entry_points={
        'console_scripts': [
            'ai-data-platform=scripts.main:main',
            'validate-agents=validate_agents:main',
        ],
    },
    
    # Project URLs
    project_urls={
        'Bug Reports': 'https://github.com/your-org/ai-data-analysis-platform/issues',
        'Source': 'https://github.com/your-org/ai-data-analysis-platform',
        'Documentation': 'https://ai-data-analysis-platform.readthedocs.io/',
    },
)
